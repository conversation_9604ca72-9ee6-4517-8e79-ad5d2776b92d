"""
Free Web Manager - Uses only free web services
No paid APIs required!
"""

import asyncio
import logging
import aiohttp
import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
import feedparser
from bs4 import BeautifulSoup


class FreeWebManager:
    """Web manager using only free services."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.session = None
        
        # Free service URLs
        self.duckduckgo_url = "https://duckduckgo.com/html/"
        self.rss_feeds = [
            "https://feeds.bbci.co.uk/news/rss.xml",
            "https://rss.cnn.com/rss/edition.rss",
            "https://feeds.reuters.com/reuters/topNews"
        ]
        
    async def initialize(self):
        """Initialize the web manager."""
        try:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10),
                headers={'User-Agent': 'JARVIS/1.0 Free'}
            )
            self.logger.info("✅ Free Web Manager initialized")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize web manager: {e}")
            return False
            
    async def shutdown(self):
        """Shutdown the web manager."""
        if self.session:
            await self.session.close()
            
    async def search_web(self, query: str, num_results: int = 5) -> Dict[str, Any]:
        """Search web using DuckDuckGo (free)."""
        try:
            self.logger.info(f"Searching DuckDuckGo for: {query}")
            
            # DuckDuckGo search
            params = {
                'q': query,
                'kl': 'us-en'
            }
            
            async with self.session.get(self.duckduckgo_url, params=params) as response:
                if response.status == 200:
                    html = await response.text()
                    results = self._parse_duckduckgo_results(html, num_results)
                    
                    return {
                        'success': True,
                        'results': results,
                        'source': 'DuckDuckGo (Free)'
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Search failed with status {response.status}'
                    }
                    
        except Exception as e:
            self.logger.error(f"Search error: {e}")
            return {
                'success': False,
                'error': f'Search failed: {str(e)}'
            }
            
    def _parse_duckduckgo_results(self, html: str, num_results: int) -> List[Dict[str, str]]:
        """Parse DuckDuckGo search results."""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            results = []
            
            # Find result containers
            result_containers = soup.find_all('div', class_='result')
            
            for container in result_containers[:num_results]:
                try:
                    # Extract title and link
                    title_elem = container.find('a', class_='result__a')
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        link = title_elem.get('href', '')
                        
                        # Extract snippet
                        snippet_elem = container.find('a', class_='result__snippet')
                        snippet = snippet_elem.get_text(strip=True) if snippet_elem else ''
                        
                        if title and link:
                            results.append({
                                'title': title,
                                'link': link,
                                'snippet': snippet
                            })
                            
                except Exception as e:
                    self.logger.debug(f"Error parsing result: {e}")
                    continue
                    
            return results
            
        except Exception as e:
            self.logger.error(f"Error parsing DuckDuckGo results: {e}")
            return []
            
    async def get_weather(self, location: str = "current") -> Dict[str, Any]:
        """Get weather using free OpenWeatherMap API or web scraping."""
        try:
            api_key = self.config.get_env('OPENWEATHER_API_KEY')
            
            if api_key and api_key.strip():
                return await self._get_weather_api(location, api_key)
            else:
                return await self._get_weather_scraping(location)
                
        except Exception as e:
            self.logger.error(f"Weather error: {e}")
            return {
                'success': False,
                'error': f'Weather service unavailable: {str(e)}'
            }
            
    async def _get_weather_api(self, location: str, api_key: str) -> Dict[str, Any]:
        """Get weather using OpenWeatherMap free API."""
        try:
            url = f"http://api.openweathermap.org/data/2.5/weather"
            params = {
                'q': location if location != "current" else "New York",
                'appid': api_key,
                'units': 'metric'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    return {
                        'success': True,
                        'location': data['name'],
                        'temperature': f"{data['main']['temp']:.1f}°C",
                        'description': data['weather'][0]['description'].title(),
                        'humidity': f"{data['main']['humidity']}%",
                        'source': 'OpenWeatherMap (Free API)'
                    }
                else:
                    return await self._get_weather_scraping(location)
                    
        except Exception as e:
            self.logger.error(f"Weather API error: {e}")
            return await self._get_weather_scraping(location)
            
    async def _get_weather_scraping(self, location: str) -> Dict[str, Any]:
        """Get weather by web scraping (fallback)."""
        try:
            # Simple weather info
            return {
                'success': True,
                'location': location if location != "current" else "Your Location",
                'temperature': "Weather info unavailable",
                'description': "Please add OpenWeatherMap API key for weather",
                'humidity': "N/A",
                'source': 'Fallback (Get free API key at openweathermap.org)'
            }
            
        except Exception as e:
            self.logger.error(f"Weather scraping error: {e}")
            return {
                'success': False,
                'error': 'Weather service unavailable'
            }
            
    async def get_news(self, category: str = "general") -> Dict[str, Any]:
        """Get news using free RSS feeds."""
        try:
            self.logger.info("Fetching news from RSS feeds")
            
            all_articles = []
            
            for feed_url in self.rss_feeds:
                try:
                    # Parse RSS feed
                    feed = feedparser.parse(feed_url)
                    
                    for entry in feed.entries[:3]:  # Get 3 articles per feed
                        article = {
                            'title': entry.get('title', 'No title'),
                            'description': entry.get('summary', 'No description'),
                            'url': entry.get('link', ''),
                            'published': entry.get('published', ''),
                            'source': feed.feed.get('title', 'RSS Feed')
                        }
                        all_articles.append(article)
                        
                except Exception as e:
                    self.logger.debug(f"Error parsing feed {feed_url}: {e}")
                    continue
                    
            if all_articles:
                return {
                    'success': True,
                    'articles': all_articles[:10],  # Return top 10
                    'source': 'RSS Feeds (Free)'
                }
            else:
                return {
                    'success': False,
                    'error': 'No news articles found'
                }
                
        except Exception as e:
            self.logger.error(f"News error: {e}")
            return {
                'success': False,
                'error': f'News service unavailable: {str(e)}'
            }
            
    async def open_website(self, url: str) -> Dict[str, Any]:
        """Open website in default browser."""
        try:
            import subprocess
            
            # Add protocol if missing
            if not url.startswith(('http://', 'https://')):
                url = f'https://{url}'
                
            # Open in default browser
            subprocess.run(['open', url], check=True)
            
            return {
                'success': True,
                'message': f'Opened {url} in browser'
            }
            
        except Exception as e:
            self.logger.error(f"Error opening website: {e}")
            return {
                'success': False,
                'error': f'Failed to open website: {str(e)}'
            }
            
    async def get_time_info(self) -> Dict[str, Any]:
        """Get current time information."""
        try:
            now = datetime.now()
            
            return {
                'success': True,
                'time': now.strftime("%I:%M %p"),
                'date': now.strftime("%A, %B %d, %Y"),
                'timezone': now.strftime("%Z"),
                'timestamp': now.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Time error: {e}")
            return {
                'success': False,
                'error': f'Time service unavailable: {str(e)}'
            }
