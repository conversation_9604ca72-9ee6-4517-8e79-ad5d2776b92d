"""
Web Manager - Internet access and web services
"""

import asyncio
import logging
import aiohttp
import json
from typing import Dict, List, Optional, Any
from urllib.parse import quote_plus
import requests
from bs4 import BeautifulSoup


class WebManager:
    """Manages web browsing and internet services."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Web settings
        self.default_browser = config.get('web', {}).get('default_browser', 'Safari')
        self.search_engine = config.get('web', {}).get('search_engine', 'https://www.google.com/search?q=')
        self.user_agent = config.get('web', {}).get('user_agent', 'JARVIS/1.0')
        
        # Session for HTTP requests
        self.session = None
        
        # API keys
        self.weather_api_key = config.get('openweather_api_key')
        self.google_search_api_key = config.get('google_search_api_key')
        self.google_search_engine_id = config.get('google_search_engine_id')
        
    async def initialize(self):
        """Initialize web manager."""
        self.logger.info("Initializing Web Manager...")
        
        try:
            # Create aiohttp session
            self.session = aiohttp.ClientSession(
                headers={'User-Agent': self.user_agent},
                timeout=aiohttp.ClientTimeout(total=30)
            )
            
            self.logger.info("✅ Web Manager initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Web Manager: {e}")
            raise
            
    async def search_web(self, query: str, num_results: int = 5) -> Dict[str, Any]:
        """Search the web for information."""
        try:
            if self.google_search_api_key and self.google_search_engine_id:
                # Use Google Custom Search API
                return await self._google_custom_search(query, num_results)
            else:
                # Fallback to web scraping
                return await self._scrape_search_results(query, num_results)
                
        except Exception as e:
            self.logger.error(f"Error in web search: {e}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }
            
    async def _google_custom_search(self, query: str, num_results: int) -> Dict[str, Any]:
        """Use Google Custom Search API."""
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': self.google_search_api_key,
                'cx': self.google_search_engine_id,
                'q': query,
                'num': min(num_results, 10)
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    results = []
                    for item in data.get('items', []):
                        results.append({
                            'title': item.get('title', ''),
                            'url': item.get('link', ''),
                            'snippet': item.get('snippet', '')
                        })
                        
                    return {
                        'success': True,
                        'query': query,
                        'results': results,
                        'total_results': data.get('searchInformation', {}).get('totalResults', '0')
                    }
                else:
                    return {'success': False, 'error': f'API error: {response.status}'}
                    
        except Exception as e:
            self.logger.error(f"Google Custom Search error: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _scrape_search_results(self, query: str, num_results: int) -> Dict[str, Any]:
        """Scrape search results as fallback."""
        try:
            search_url = f"{self.search_engine}{quote_plus(query)}"
            
            async with self.session.get(search_url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    results = []
                    # Parse Google search results
                    for result in soup.find_all('div', class_='g')[:num_results]:
                        title_elem = result.find('h3')
                        link_elem = result.find('a')
                        snippet_elem = result.find('span', class_='st') or result.find('div', class_='s')
                        
                        if title_elem and link_elem:
                            results.append({
                                'title': title_elem.get_text(),
                                'url': link_elem.get('href', ''),
                                'snippet': snippet_elem.get_text() if snippet_elem else ''
                            })
                            
                    return {
                        'success': True,
                        'query': query,
                        'results': results
                    }
                else:
                    return {'success': False, 'error': f'HTTP error: {response.status}'}
                    
        except Exception as e:
            self.logger.error(f"Web scraping error: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_weather(self, location: str = "current") -> Dict[str, Any]:
        """Get weather information."""
        try:
            if not self.weather_api_key:
                return {
                    'success': False,
                    'error': 'Weather API key not configured'
                }
                
            # If location is "current", try to get location from IP
            if location == "current":
                location = await self._get_current_location()
                
            # Get weather data
            url = "http://api.openweathermap.org/data/2.5/weather"
            params = {
                'q': location,
                'appid': self.weather_api_key,
                'units': 'metric'
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    weather_info = {
                        'location': data['name'],
                        'country': data['sys']['country'],
                        'temperature': data['main']['temp'],
                        'feels_like': data['main']['feels_like'],
                        'humidity': data['main']['humidity'],
                        'pressure': data['main']['pressure'],
                        'description': data['weather'][0]['description'],
                        'wind_speed': data['wind']['speed'],
                        'visibility': data.get('visibility', 0) / 1000  # Convert to km
                    }
                    
                    return {
                        'success': True,
                        'weather': weather_info,
                        'message': f"मौसम की जानकारी। Weather in {weather_info['location']}: {weather_info['temperature']}°C, {weather_info['description']}"
                    }
                else:
                    return {'success': False, 'error': f'Weather API error: {response.status}'}
                    
        except Exception as e:
            self.logger.error(f"Weather error: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _get_current_location(self) -> str:
        """Get current location from IP."""
        try:
            async with self.session.get('http://ip-api.com/json/') as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('city', 'Delhi')
                    
        except Exception as e:
            self.logger.warning(f"Could not get current location: {e}")
            
        return "Delhi"  # Default fallback
        
    async def get_news(self, category: str = "general", country: str = "in") -> Dict[str, Any]:
        """Get latest news."""
        try:
            # Using a free news API (you might want to get an API key for better results)
            url = f"https://newsapi.org/v2/top-headlines"
            params = {
                'country': country,
                'category': category,
                'pageSize': 5
            }
            
            # Note: You'll need to add NEWS_API_KEY to your config for this to work
            news_api_key = self.config.get('news_api_key')
            if news_api_key:
                headers = {'X-API-Key': news_api_key}
                
                async with self.session.get(url, params=params, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        articles = []
                        for article in data.get('articles', []):
                            articles.append({
                                'title': article.get('title', ''),
                                'description': article.get('description', ''),
                                'url': article.get('url', ''),
                                'source': article.get('source', {}).get('name', ''),
                                'published_at': article.get('publishedAt', '')
                            })
                            
                        return {
                            'success': True,
                            'articles': articles,
                            'message': f"समाचार मिल गए। Found {len(articles)} news articles"
                        }
                        
            # Fallback: scrape news from a reliable source
            return await self._scrape_news()
            
        except Exception as e:
            self.logger.error(f"News error: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _scrape_news(self) -> Dict[str, Any]:
        """Scrape news as fallback."""
        try:
            # Simple news scraping from BBC or similar
            url = "https://www.bbc.com/news"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    articles = []
                    # This is a simplified scraper - you might need to adjust selectors
                    for headline in soup.find_all('h3')[:5]:
                        link = headline.find('a')
                        if link:
                            articles.append({
                                'title': headline.get_text().strip(),
                                'url': f"https://www.bbc.com{link.get('href', '')}",
                                'source': 'BBC News'
                            })
                            
                    return {
                        'success': True,
                        'articles': articles,
                        'message': f"समाचार मिल गए। Found {len(articles)} news headlines"
                    }
                    
        except Exception as e:
            self.logger.error(f"News scraping error: {e}")
            
        return {'success': False, 'error': 'Could not fetch news'}
        
    async def open_website(self, url: str) -> Dict[str, Any]:
        """Open a website in the default browser."""
        try:
            import subprocess
            
            # Ensure URL has protocol
            if not url.startswith(('http://', 'https://')):
                url = f"https://{url}"
                
            # Open in default browser
            subprocess.run(['open', url], check=True)
            
            return {
                'success': True,
                'message': f"वेबसाइट खोली गई। Opened {url}",
                'url': url
            }
            
        except Exception as e:
            self.logger.error(f"Error opening website: {e}")
            return {'success': False, 'error': str(e)}
            
    async def shutdown(self):
        """Shutdown web manager."""
        if self.session:
            await self.session.close()
            
        self.logger.info("✅ Web Manager shutdown complete")
