# JARVIS Configuration

# Speech Settings
speech:
  recognition:
    language: "hi-IN,en-US"  # Hindi and English
    timeout: 5
    phrase_timeout: 1
    energy_threshold: 300
    
  synthesis:
    voice_id: "com.apple.speech.synthesis.voice.samantha"
    rate: 200
    volume: 0.8
    hindi_voice: "com.apple.speech.synthesis.voice.lekha"
    
  wake_word: "jarvis"
  
# AI Settings
ai:
  model: "gpt-4"
  max_tokens: 1000
  temperature: 0.7
  context_window: 10
  
# GUI Settings
gui:
  theme: "arc_reactor"
  window_size: [1200, 800]
  fullscreen: false
  transparency: 0.9
  animations: true
  
  colors:
    primary: "#00D4FF"      # Arc reactor blue
    secondary: "#FFD700"    # Gold accent
    background: "#0A0A0A"   # Dark background
    text: "#FFFFFF"         # White text
    accent: "#FF6B6B"       # Red accent
    
# System Control
system:
  allowed_operations:
    - "open_application"
    - "close_application"
    - "file_operations"
    - "system_settings"
    - "volume_control"
    - "brightness_control"
    
  restricted_operations:
    - "system_shutdown"
    - "delete_system_files"
    
# Web Settings
web:
  default_browser: "Safari"
  search_engine: "https://www.google.com/search?q="
  user_agent: "JARVIS/1.0"
  
# Security
security:
  require_confirmation: true
  voice_authentication: false
  command_logging: true
  
# Logging
logging:
  level: "INFO"
  file: "logs/jarvis.log"
  max_size: "10MB"
  backup_count: 5
