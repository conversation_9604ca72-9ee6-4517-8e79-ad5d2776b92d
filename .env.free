# =============================================================================
# JARVIS AI Assistant - COMPLETELY FREE Configuration
# =============================================================================
# This configuration uses NO paid APIs - everything is free!

# =============================================================================
# AI/LLM Services - OFFLINE MODE (completely free)
# =============================================================================
USE_OFFLINE_AI=true
OPENAI_API_KEY=

# =============================================================================
# Speech Services - SYSTEM SPEECH (completely free)
# =============================================================================
USE_SYSTEM_SPEECH=true
GOOGLE_SPEECH_API_KEY=

# =============================================================================
# Web Services - FREE SEARCH (completely free)
# =============================================================================
USE_DUCKDUCKGO_SEARCH=true
GOOGLE_SEARCH_API_KEY=

# =============================================================================
# Weather API - FREE TIER (1000 calls/day free)
# =============================================================================
# Get your free API key from: https://openweathermap.org/api
OPENWEATHER_API_KEY=your_free_openweather_api_key_here

# =============================================================================
# News Services - RSS FEEDS (completely free)
# =============================================================================
USE_RSS_NEWS=true
NEWS_API_KEY=

# =============================================================================
# Email Services (optional - use your existing email)
# =============================================================================
EMAIL_ADDRESS=
EMAIL_PASSWORD=
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587

# =============================================================================
# System Settings
# =============================================================================
DEBUG=false
LOG_LEVEL=INFO

# =============================================================================
# SETUP INSTRUCTIONS:
# =============================================================================
# 1. Copy this file to .env: cp .env.free .env
# 2. Get ONE free API key from OpenWeatherMap (optional but recommended)
# 3. Run: python3 main.py
# 
# That's it! JARVIS will work with completely free services.
# =============================================================================
