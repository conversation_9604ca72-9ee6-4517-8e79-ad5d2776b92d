#!/usr/bin/env python3
"""
JARVIS Setup Script
Installs dependencies and sets up the JARVIS AI Assistant
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version}")


def check_macos():
    """Check if running on macOS."""
    if platform.system() != "Darwin":
        print("⚠️ JARVIS is optimized for macOS. Some features may not work on other platforms.")
    else:
        print("✅ Running on macOS")


def install_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)


def install_system_dependencies():
    """Install system dependencies for macOS."""
    print("\n🔧 Checking system dependencies...")
    
    # Check for Homebrew
    try:
        subprocess.check_output(["which", "brew"])
        print("✅ Homebrew found")
        
        # Install system dependencies
        dependencies = [
            "portaudio",  # For PyAudio
            "ffmpeg",     # For audio processing
        ]
        
        for dep in dependencies:
            try:
                subprocess.check_call(["brew", "install", dep], 
                                    stdout=subprocess.DEVNULL, 
                                    stderr=subprocess.DEVNULL)
                print(f"✅ Installed {dep}")
            except subprocess.CalledProcessError:
                print(f"⚠️ Could not install {dep} (may already be installed)")
                
    except subprocess.CalledProcessError:
        print("⚠️ Homebrew not found. Please install Homebrew first:")
        print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")


def setup_directories():
    """Create necessary directories."""
    print("\n📁 Setting up directories...")
    
    directories = [
        "logs",
        "data",
        "config",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")


def setup_config():
    """Set up configuration files."""
    print("\n⚙️ Setting up configuration...")
    
    # Copy .env.example to .env if it doesn't exist
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        import shutil
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("📝 Please edit .env file with your API keys")
    else:
        print("✅ Configuration files already exist")


def check_permissions():
    """Check for necessary permissions on macOS."""
    print("\n🔐 Checking permissions...")
    
    print("📋 JARVIS requires the following permissions:")
    print("   • Microphone access (for voice recognition)")
    print("   • Accessibility access (for system control)")
    print("   • Screen recording (for some automation features)")
    print("")
    print("💡 You will be prompted to grant these permissions when you first run JARVIS.")
    print("   Go to System Preferences > Security & Privacy to manage permissions.")


def create_launch_script():
    """Create a launch script for easy startup."""
    print("\n🚀 Creating launch script...")
    
    launch_script = """#!/bin/bash
# JARVIS Launch Script

echo "🤖 Starting JARVIS AI Assistant..."
echo "=================================="

# Check if virtual environment exists
if [ -d "venv" ]; then
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
fi

# Run JARVIS
python3 main.py

echo "👋 JARVIS shutdown complete"
"""
    
    with open("start_jarvis.sh", "w") as f:
        f.write(launch_script)
    
    # Make executable
    os.chmod("start_jarvis.sh", 0o755)
    print("✅ Created start_jarvis.sh launch script")


def main():
    """Main setup function."""
    print("🤖 JARVIS AI Assistant Setup")
    print("=" * 50)
    
    # Check system requirements
    check_python_version()
    check_macos()
    
    # Install dependencies
    install_system_dependencies()
    install_dependencies()
    
    # Setup project
    setup_directories()
    setup_config()
    create_launch_script()
    
    # Check permissions
    check_permissions()
    
    print("\n" + "=" * 50)
    print("🎉 JARVIS setup complete!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your API keys")
    print("2. Run: python3 main.py")
    print("   Or: ./start_jarvis.sh")
    print("\n💡 For help, check README.md")
    print("🔗 Repository: https://github.com/your-username/jarvis")


if __name__ == "__main__":
    main()
