# =============================================================================
# JARVIS AI Assistant - FREE API Configuration
# =============================================================================
# This configuration uses FREE services wherever possible
# No paid APIs required for basic functionality!

# =============================================================================
# AI/LLM Services (Choose ONE - all have free tiers)
# =============================================================================

# Option 1: OpenAI (FREE tier: $5 credit for new users)
# Get free API key: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Option 2: Anthropic Claude (FREE tier available)
# Get free API key: https://console.anthropic.com/
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Option 3: Use LOCAL/OFFLINE AI (completely free)
# Set to 'true' to use offline AI (no API key needed)
USE_OFFLINE_AI=false

# =============================================================================
# Speech Services (FREE options available)
# =============================================================================

# Option 1: Use macOS built-in speech (completely FREE)
USE_SYSTEM_SPEECH=true

# Option 2: Google Speech API (FREE tier: 60 minutes/month)
# Get free API key: https://cloud.google.com/speech-to-text
# GOOGLE_SPEECH_API_KEY=your_google_speech_api_key_here

# Option 3: Azure Speech (FREE tier: 5 hours/month)
# Get free API key: https://azure.microsoft.com/en-us/services/cognitive-services/speech-services/
# AZURE_SPEECH_KEY=your_azure_speech_key_here
# AZURE_SPEECH_REGION=your_azure_region_here

# =============================================================================
# Web Services (FREE options)
# =============================================================================

# Option 1: DuckDuckGo Search (completely FREE, no API key needed)
USE_DUCKDUCKGO_SEARCH=true

# Option 2: Google Custom Search (FREE tier: 100 searches/day)
# Get free API key: https://developers.google.com/custom-search/v1/introduction
# GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
# GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# Option 3: Bing Search (FREE tier available)
# BING_SEARCH_API_KEY=your_bing_search_api_key_here

# =============================================================================
# Weather API (FREE options)
# =============================================================================

# Option 1: OpenWeatherMap (FREE tier: 1000 calls/day)
# Get free API key: https://openweathermap.org/api
OPENWEATHER_API_KEY=your_openweather_api_key_here

# Option 2: WeatherAPI (FREE tier: 1 million calls/month)
# Get free API key: https://www.weatherapi.com/
# WEATHER_API_KEY=your_weather_api_key_here

# Option 3: Use web scraping for weather (no API key needed)
# USE_WEB_SCRAPING_WEATHER=true

# =============================================================================
# News Services (FREE options)
# =============================================================================

# Option 1: NewsAPI (FREE tier: 1000 requests/day)
# Get free API key: https://newsapi.org/
# NEWS_API_KEY=your_news_api_key_here

# Option 2: Use RSS feeds (completely FREE)
USE_RSS_NEWS=true

# =============================================================================
# Email Services (FREE - use your existing email)
# =============================================================================

# Gmail (free with app password)
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_password_here
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587

# =============================================================================
# System Settings
# =============================================================================
DEBUG=false
LOG_LEVEL=INFO

# =============================================================================
# QUICK SETUP FOR COMPLETELY FREE USAGE:
# =============================================================================
# 1. Set USE_OFFLINE_AI=true (no AI API key needed)
# 2. Set USE_SYSTEM_SPEECH=true (uses macOS built-in speech)
# 3. Set USE_DUCKDUCKGO_SEARCH=true (free web search)
# 4. Set USE_RSS_NEWS=true (free news)
# 5. Get free OpenWeatherMap API key (1000 calls/day free)
#
# This gives you a fully functional JARVIS with NO paid APIs!
