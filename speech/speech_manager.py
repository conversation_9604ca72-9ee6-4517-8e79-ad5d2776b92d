"""
Speech Manager - Handles speech recognition and synthesis
Supports Hindi and English with mixed language capabilities
"""

import asyncio
import logging
import threading
import queue
import time
from typing import Callable, Optional, Dict, Any

import speech_recognition as sr
import pyttsx3
import pygame
from gtts import gTTS
import io
import tempfile
import os


class SpeechManager:
    """Manages speech recognition and text-to-speech functionality."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Speech recognition
        self.recognizer = None
        self.microphone = None
        self.is_listening = False
        self.listen_thread = None
        
        # Text-to-speech
        self.tts_engine = None
        self.is_speaking = False
        
        # Audio processing
        pygame.mixer.init()
        
        # Callbacks
        self.on_wake_word_detected: Optional[Callable] = None
        self.on_command_recognized: Optional[Callable] = None
        self.on_speech_started: Optional[Callable] = None
        self.on_speech_ended: Optional[Callable] = None
        
        # Wake word detection
        self.wake_word = config.get('speech', {}).get('wake_word', 'jarvis').lower()
        self.wake_word_variants = [
            'jarvis', 'जार्विस', 'jarwis', 'jarvish'
        ]
        
        # Audio queue for processing
        self.audio_queue = queue.Queue()
        
    async def initialize(self):
        """Initialize speech recognition and synthesis."""
        self.logger.info("Initializing Speech Manager...")
        
        try:
            # Initialize speech recognition
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Adjust for ambient noise
            with self.microphone as source:
                self.logger.info("Adjusting for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
                
            # Configure recognition settings
            speech_config = self.config.get('speech', {})
            self.recognizer.energy_threshold = speech_config.get('recognition', {}).get('energy_threshold', 300)
            self.recognizer.timeout = speech_config.get('recognition', {}).get('timeout', 5)
            self.recognizer.phrase_timeout = speech_config.get('recognition', {}).get('phrase_timeout', 1)
            
            # Initialize TTS engine
            self.tts_engine = pyttsx3.init()
            
            # Configure TTS settings
            tts_config = speech_config.get('synthesis', {})
            rate = tts_config.get('rate', 200)
            volume = tts_config.get('volume', 0.8)
            
            self.tts_engine.setProperty('rate', rate)
            self.tts_engine.setProperty('volume', volume)
            
            # Set voice (try to use specified voice)
            voices = self.tts_engine.getProperty('voices')
            voice_id = tts_config.get('voice_id')
            if voice_id:
                for voice in voices:
                    if voice_id in voice.id:
                        self.tts_engine.setProperty('voice', voice.id)
                        break
                        
            self.logger.info("✅ Speech Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Speech Manager: {e}")
            raise
            
    async def start_listening(self):
        """Start continuous speech recognition."""
        if self.is_listening:
            return
            
        self.is_listening = True
        self.listen_thread = threading.Thread(target=self._listen_continuously, daemon=True)
        self.listen_thread.start()
        
        self.logger.info("🎤 Started listening for speech...")
        
    async def stop_listening(self):
        """Stop speech recognition."""
        self.is_listening = False
        if self.listen_thread:
            self.listen_thread.join(timeout=2)
            
        self.logger.info("🔇 Stopped listening for speech")
        
    def _listen_continuously(self):
        """Continuously listen for speech in background thread."""
        while self.is_listening:
            try:
                with self.microphone as source:
                    # Listen for audio with timeout
                    audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=5)
                    
                # Process audio in separate thread to avoid blocking
                threading.Thread(
                    target=self._process_audio,
                    args=(audio,),
                    daemon=True
                ).start()
                
            except sr.WaitTimeoutError:
                # Normal timeout, continue listening
                continue
            except Exception as e:
                self.logger.error(f"Error in continuous listening: {e}")
                time.sleep(1)  # Brief pause before retrying
                
    def _process_audio(self, audio):
        """Process captured audio for speech recognition."""
        try:
            # Try Google Speech Recognition first (supports Hindi)
            text = self.recognizer.recognize_google(
                audio,
                language="hi-IN,en-US"  # Hindi and English
            )
            
            confidence = 0.8  # Google doesn't provide confidence, assume high
            
            self.logger.debug(f"Recognized speech: '{text}' (confidence: {confidence:.2f})")
            
            # Check for wake word
            if self._contains_wake_word(text.lower()):
                if self.on_wake_word_detected:
                    asyncio.create_task(self.on_wake_word_detected())
                return
                
            # Process as command if confidence is high enough
            if confidence > 0.6 and self.on_command_recognized:
                asyncio.create_task(self.on_command_recognized(text, confidence))
                
        except sr.UnknownValueError:
            # Speech was unintelligible
            self.logger.debug("Could not understand audio")
        except sr.RequestError as e:
            # API error
            self.logger.error(f"Speech recognition API error: {e}")
            
            # Fallback to offline recognition
            try:
                text = self.recognizer.recognize_sphinx(audio)
                confidence = 0.6  # Lower confidence for offline recognition
                
                if self._contains_wake_word(text.lower()):
                    if self.on_wake_word_detected:
                        asyncio.create_task(self.on_wake_word_detected())
                elif confidence > 0.5 and self.on_command_recognized:
                    asyncio.create_task(self.on_command_recognized(text, confidence))
                    
            except Exception as fallback_error:
                self.logger.error(f"Fallback recognition failed: {fallback_error}")
                
    def _contains_wake_word(self, text: str) -> bool:
        """Check if text contains wake word."""
        text = text.lower().strip()
        
        for variant in self.wake_word_variants:
            if variant in text:
                return True
                
        return False
        
    async def speak(self, text: str, language: str = "auto"):
        """Convert text to speech and play it."""
        if self.is_speaking:
            self.logger.warning("Already speaking, queuing message...")
            
        self.is_speaking = True
        
        try:
            if self.on_speech_started:
                self.on_speech_started()
                
            # Detect language if auto
            if language == "auto":
                language = self._detect_speech_language(text)
                
            # Use different TTS based on language
            if language == "hindi" or "hindi" in language:
                await self._speak_with_gtts(text, "hi")
            elif language == "mixed":
                # For mixed language, split and speak parts separately
                await self._speak_mixed_language(text)
            else:
                # Use system TTS for English
                await self._speak_with_system_tts(text)
                
        except Exception as e:
            self.logger.error(f"Error in text-to-speech: {e}")
        finally:
            self.is_speaking = False
            if self.on_speech_ended:
                self.on_speech_ended()
                
    async def _speak_with_system_tts(self, text: str):
        """Speak using system TTS engine."""
        def speak_sync():
            self.tts_engine.say(text)
            self.tts_engine.runAndWait()
            
        # Run in thread to avoid blocking
        await asyncio.get_event_loop().run_in_executor(None, speak_sync)
        
    async def _speak_with_gtts(self, text: str, lang: str = "hi"):
        """Speak using Google TTS for better Hindi support."""
        try:
            # Generate speech
            tts = gTTS(text=text, lang=lang, slow=False)
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as tmp_file:
                tts.save(tmp_file.name)
                
                # Play using pygame
                pygame.mixer.music.load(tmp_file.name)
                pygame.mixer.music.play()
                
                # Wait for playback to complete
                while pygame.mixer.music.get_busy():
                    await asyncio.sleep(0.1)
                    
                # Clean up
                os.unlink(tmp_file.name)
                
        except Exception as e:
            self.logger.error(f"gTTS error: {e}")
            # Fallback to system TTS
            await self._speak_with_system_tts(text)
            
    async def _speak_mixed_language(self, text: str):
        """Handle mixed Hindi-English text."""
        # Simple approach: split by sentences and detect language per sentence
        sentences = text.split('.')
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            lang = self._detect_speech_language(sentence)
            
            if "hindi" in lang:
                await self._speak_with_gtts(sentence, "hi")
            else:
                await self._speak_with_system_tts(sentence)
                
            # Brief pause between sentences
            await asyncio.sleep(0.3)
            
    def _detect_speech_language(self, text: str) -> str:
        """Detect language for TTS selection."""
        import re
        
        hindi_chars = len(re.findall(r'[\u0900-\u097F]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        if hindi_chars > 0 and english_chars > 0:
            return "mixed"
        elif hindi_chars > 0:
            return "hindi"
        else:
            return "english"
            
    async def shutdown(self):
        """Shutdown speech manager."""
        await self.stop_listening()
        
        if self.tts_engine:
            self.tts_engine.stop()
            
        pygame.mixer.quit()
        
        self.logger.info("✅ Speech Manager shutdown complete")
