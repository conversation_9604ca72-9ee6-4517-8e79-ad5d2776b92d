#!/usr/bin/env python3
"""
JARVIS Demo Script
Demonstrates JARVIS capabilities without requiring full setup
"""

import asyncio
import sys
import re
from pathlib import Path


class JarvisDemo:
    """Demo class to showcase JARVIS capabilities."""

    def __init__(self):
        pass

    async def run_demo(self):
        """Run the JARVIS demo."""
        print("🤖 JARVIS AI Assistant Demo")
        print("=" * 50)
        print("This demo shows JARVIS's natural language understanding")
        print("without requiring full system setup or API keys.\n")

        print("✅ Demo mode initialized (no dependencies required)")
            
        # Demo commands
        demo_commands = [
            "JARVIS, open Safari",
            "volume up karo",
            "weather batao Delhi mein",
            "search for Python tutorials",
            "WhatsApp pe message bhejo John ko",
            "music chalao Spotify mein",
            "brightness 50% set karo",
            "email bhejo boss ko",
            "time kya hai",
            "news dikhao latest",
            "system band karo",
            "hello JARVIS kaise ho"
        ]
        
        print("\n🗣️ Testing Voice Command Understanding:")
        print("-" * 50)
        
        for i, command in enumerate(demo_commands, 1):
            print(f"\n{i:2d}. Command: '{command}'")
            self._simple_analysis(command)
                
        self._show_capabilities()
        self._show_next_steps()
        
    def _display_intent_analysis(self, intent):
        """Display detailed intent analysis."""
        action = intent.get('action', 'unknown')
        target = intent.get('target', 'unknown')
        category = intent.get('category', 'unknown')
        language = intent.get('language', 'unknown')
        confidence = intent.get('confidence', 0.0)
        
        print(f"    🎯 Action: {action}")
        print(f"    🎪 Target: {target}")
        print(f"    📂 Category: {category}")
        print(f"    🌐 Language: {language}")
        print(f"    📊 Confidence: {confidence:.2f}")
        
        # Show what JARVIS would do
        response = self._generate_demo_response(action, target, category)
        print(f"    🤖 JARVIS would: {response}")
        
    def _simple_analysis(self, command):
        """Simple analysis without full NLP."""
        command_lower = command.lower()
        
        if 'open' in command_lower or 'खोलो' in command_lower:
            print("    🎯 Action: open")
            print("    📂 Category: system_control")
            print("    🤖 JARVIS would: Open the specified application")
            
        elif 'volume' in command_lower or 'आवाज़' in command_lower:
            print("    🎯 Action: volume control")
            print("    📂 Category: system_control")
            print("    🤖 JARVIS would: Adjust system volume")
            
        elif 'search' in command_lower or 'खोजो' in command_lower:
            print("    🎯 Action: search")
            print("    📂 Category: web_search")
            print("    🤖 JARVIS would: Search the web for information")
            
        elif 'weather' in command_lower or 'मौसम' in command_lower:
            print("    🎯 Action: get weather")
            print("    📂 Category: information")
            print("    🤖 JARVIS would: Get weather information")
            
        else:
            print("    🎯 Action: general conversation")
            print("    📂 Category: general")
            print("    🤖 JARVIS would: Respond conversationally")
            
    def _generate_demo_response(self, action, target, category):
        """Generate demo response based on intent."""
        responses = {
            ('open', 'safari'): "Launch Safari browser",
            ('open', 'music'): "Open Music application",
            ('search', 'unknown'): "Search the web for the query",
            ('get', 'weather'): "Get current weather information",
            ('set', 'volume'): "Adjust system volume",
            ('send', 'email'): "Open Mail application",
            ('play', 'music'): "Start playing music",
        }
        
        key = (action, target)
        return responses.get(key, f"Execute {action} command for {target}")
        
    def _show_capabilities(self):
        """Show JARVIS capabilities."""
        print("\n" + "=" * 50)
        print("🚀 JARVIS Capabilities")
        print("=" * 50)
        
        capabilities = {
            "🗣️ Speech Recognition": [
                "Hindi and English voice commands",
                "Mixed language support",
                "Wake word detection ('JARVIS')",
                "Continuous listening mode"
            ],
            "🖥️ System Control": [
                "Open/close applications",
                "Volume and brightness control",
                "File operations",
                "System information"
            ],
            "🌐 Web Integration": [
                "Web search",
                "Weather information",
                "News updates",
                "Website opening"
            ],
            "🧠 AI Processing": [
                "Natural language understanding",
                "Intent recognition",
                "Context awareness",
                "Conversation memory"
            ],
            "🎨 User Interface": [
                "Iron Man inspired GUI",
                "Arc reactor animation",
                "Voice visualization",
                "Real-time status display"
            ]
        }
        
        for category, features in capabilities.items():
            print(f"\n{category}")
            for feature in features:
                print(f"  • {feature}")
                
    def _show_next_steps(self):
        """Show next steps for full setup."""
        print("\n" + "=" * 50)
        print("🎯 Next Steps - Full JARVIS Setup")
        print("=" * 50)
        
        steps = [
            "1. 🔑 Get API Keys:",
            "   • OpenAI API key (required for AI processing)",
            "   • Google Search API key (optional, for web search)",
            "   • OpenWeather API key (optional, for weather)",
            "",
            "2. 📦 Install Dependencies:",
            "   • Run: python3 setup.py",
            "   • This installs all required packages",
            "",
            "3. ⚙️ Configure Settings:",
            "   • Edit .env file with your API keys",
            "   • Customize config.yaml if needed",
            "",
            "4. 🔐 Grant Permissions:",
            "   • Microphone access (for voice recognition)",
            "   • Accessibility access (for system control)",
            "",
            "5. 🚀 Launch JARVIS:",
            "   • Run: python3 main.py",
            "   • Or use: ./start_jarvis.sh",
            "",
            "6. 🎤 Test Voice Commands:",
            "   • Say 'JARVIS' to activate",
            "   • Try: 'JARVIS, hello'",
            "   • Use any commands from the demo above"
        ]
        
        for step in steps:
            print(step)
            
        print("\n💡 Tips:")
        print("• Start with simple commands like 'JARVIS, hello'")
        print("• Use both Hindi and English naturally")
        print("• Check logs/jarvis.log for troubleshooting")
        print("• Run test_jarvis.py to verify setup")
        
        print("\n🔗 Resources:")
        print("• README.md - Complete setup guide")
        print("• config.yaml - Configuration options")
        print("• .env.example - API key template")


async def main():
    """Run the demo."""
    demo = JarvisDemo()
    await demo.run_demo()


if __name__ == "__main__":
    asyncio.run(main())
