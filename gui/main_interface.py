"""
JARVIS Main GUI Interface - Iron Man inspired sci-fi interface
"""

import asyncio
import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
import threading
import time
import math
from typing import Optional, Callable
import logging


class JarvisGUI:
    """Main JARVIS GUI with Iron Man inspired design."""
    
    def __init__(self, config, jarvis_core):
        self.config = config
        self.jarvis_core = jarvis_core
        self.logger = logging.getLogger(__name__)
        
        # GUI settings
        gui_config = config.get('gui', {})
        self.theme = gui_config.get('theme', 'arc_reactor')
        self.window_size = gui_config.get('window_size', [1200, 800])
        self.colors = gui_config.get('colors', {})
        
        # Set CustomTkinter theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Main window
        self.root = None
        self.is_running = False
        
        # GUI components
        self.status_label = None
        self.command_entry = None
        self.response_text = None
        self.arc_reactor_canvas = None
        self.voice_visualizer = None
        
        # Animation variables
        self.arc_reactor_angle = 0
        self.pulse_intensity = 0
        self.is_listening_animation = False
        
        # Set up callbacks
        self.jarvis_core.on_status_changed = self._on_status_changed
        self.jarvis_core.on_response_ready = self._on_response_ready
        
    async def run(self):
        """Run the GUI interface."""
        self.logger.info("Starting JARVIS GUI...")
        
        # Create GUI in main thread
        self._create_gui()
        
        # Start JARVIS listening
        await self.jarvis_core.start_listening()
        
        # Start GUI main loop
        self.is_running = True
        self._start_animations()
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            await self.cleanup()
            
    def _create_gui(self):
        """Create the main GUI interface."""
        # Main window
        self.root = ctk.CTk()
        self.root.title("JARVIS - AI Assistant")
        self.root.geometry(f"{self.window_size[0]}x{self.window_size[1]}")
        self.root.configure(fg_color=self.colors.get('background', '#0A0A0A'))
        
        # Make window semi-transparent
        self.root.attributes('-alpha', self.config.get('gui', {}).get('transparency', 0.95))
        
        # Create main layout
        self._create_header()
        self._create_arc_reactor()
        self._create_status_panel()
        self._create_command_interface()
        self._create_response_panel()
        self._create_system_monitor()
        
        # Bind events
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.command_entry.bind('<Return>', self._on_command_submit)
        
    def _create_header(self):
        """Create the header with JARVIS branding."""
        header_frame = ctk.CTkFrame(
            self.root,
            height=80,
            fg_color=self.colors.get('background', '#0A0A0A'),
            corner_radius=0
        )
        header_frame.pack(fill="x", padx=10, pady=(10, 5))
        header_frame.pack_propagate(False)
        
        # JARVIS title
        title_label = ctk.CTkLabel(
            header_frame,
            text="J.A.R.V.I.S",
            font=ctk.CTkFont(family="Helvetica", size=36, weight="bold"),
            text_color=self.colors.get('primary', '#00D4FF')
        )
        title_label.pack(side="left", padx=20, pady=20)
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="Just A Rather Very Intelligent System",
            font=ctk.CTkFont(family="Helvetica", size=14),
            text_color=self.colors.get('secondary', '#FFD700')
        )
        subtitle_label.pack(side="left", padx=(0, 20), pady=20)
        
        # Status indicator
        self.status_label = ctk.CTkLabel(
            header_frame,
            text="● INITIALIZING",
            font=ctk.CTkFont(family="Helvetica", size=16, weight="bold"),
            text_color=self.colors.get('accent', '#FF6B6B')
        )
        self.status_label.pack(side="right", padx=20, pady=20)
        
    def _create_arc_reactor(self):
        """Create the arc reactor visualization."""
        reactor_frame = ctk.CTkFrame(
            self.root,
            height=200,
            fg_color=self.colors.get('background', '#0A0A0A'),
            corner_radius=15
        )
        reactor_frame.pack(fill="x", padx=10, pady=5)
        reactor_frame.pack_propagate(False)
        
        # Arc reactor canvas
        self.arc_reactor_canvas = tk.Canvas(
            reactor_frame,
            width=180,
            height=180,
            bg=self.colors.get('background', '#0A0A0A'),
            highlightthickness=0
        )
        self.arc_reactor_canvas.pack(side="left", padx=20, pady=10)
        
        # Voice visualizer
        self.voice_visualizer = tk.Canvas(
            reactor_frame,
            width=400,
            height=180,
            bg=self.colors.get('background', '#0A0A0A'),
            highlightthickness=0
        )
        self.voice_visualizer.pack(side="right", padx=20, pady=10, fill="both", expand=True)
        
    def _create_status_panel(self):
        """Create system status panel."""
        status_frame = ctk.CTkFrame(
            self.root,
            height=100,
            fg_color=self.colors.get('background', '#0A0A0A'),
            corner_radius=15
        )
        status_frame.pack(fill="x", padx=10, pady=5)
        status_frame.pack_propagate(False)
        
        # System metrics
        self.cpu_label = ctk.CTkLabel(
            status_frame,
            text="CPU: ---%",
            font=ctk.CTkFont(family="Courier", size=12),
            text_color=self.colors.get('text', '#FFFFFF')
        )
        self.cpu_label.pack(side="left", padx=20, pady=10)
        
        self.memory_label = ctk.CTkLabel(
            status_frame,
            text="Memory: ---%",
            font=ctk.CTkFont(family="Courier", size=12),
            text_color=self.colors.get('text', '#FFFFFF')
        )
        self.memory_label.pack(side="left", padx=20, pady=10)
        
        self.network_label = ctk.CTkLabel(
            status_frame,
            text="Network: Connected",
            font=ctk.CTkFont(family="Courier", size=12),
            text_color=self.colors.get('primary', '#00D4FF')
        )
        self.network_label.pack(side="left", padx=20, pady=10)
        
    def _create_command_interface(self):
        """Create command input interface."""
        command_frame = ctk.CTkFrame(
            self.root,
            height=80,
            fg_color=self.colors.get('background', '#0A0A0A'),
            corner_radius=15
        )
        command_frame.pack(fill="x", padx=10, pady=5)
        command_frame.pack_propagate(False)
        
        # Command label
        command_label = ctk.CTkLabel(
            command_frame,
            text="Command Input:",
            font=ctk.CTkFont(family="Helvetica", size=14, weight="bold"),
            text_color=self.colors.get('text', '#FFFFFF')
        )
        command_label.pack(side="left", padx=20, pady=10)
        
        # Command entry
        self.command_entry = ctk.CTkEntry(
            command_frame,
            width=600,
            height=40,
            font=ctk.CTkFont(family="Helvetica", size=14),
            fg_color=self.colors.get('background', '#1A1A1A'),
            border_color=self.colors.get('primary', '#00D4FF'),
            text_color=self.colors.get('text', '#FFFFFF')
        )
        self.command_entry.pack(side="left", padx=10, pady=10, fill="x", expand=True)
        
        # Submit button
        submit_button = ctk.CTkButton(
            command_frame,
            text="Execute",
            width=100,
            height=40,
            font=ctk.CTkFont(family="Helvetica", size=14, weight="bold"),
            fg_color=self.colors.get('primary', '#00D4FF'),
            hover_color=self.colors.get('secondary', '#FFD700'),
            command=self._on_command_submit
        )
        submit_button.pack(side="right", padx=20, pady=10)
        
    def _create_response_panel(self):
        """Create response display panel."""
        response_frame = ctk.CTkFrame(
            self.root,
            fg_color=self.colors.get('background', '#0A0A0A'),
            corner_radius=15
        )
        response_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Response label
        response_label = ctk.CTkLabel(
            response_frame,
            text="JARVIS Response:",
            font=ctk.CTkFont(family="Helvetica", size=14, weight="bold"),
            text_color=self.colors.get('text', '#FFFFFF')
        )
        response_label.pack(anchor="w", padx=20, pady=(10, 0))
        
        # Response text area
        self.response_text = ctk.CTkTextbox(
            response_frame,
            font=ctk.CTkFont(family="Helvetica", size=12),
            fg_color=self.colors.get('background', '#1A1A1A'),
            text_color=self.colors.get('text', '#FFFFFF'),
            corner_radius=10
        )
        self.response_text.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Add welcome message
        welcome_msg = """🤖 JARVIS AI Assistant Initialized

Available Commands:
• Voice: Say "JARVIS" followed by your command
• Text: Type commands in Hindi, English, or mixed language

Examples:
- "JARVIS, open Safari"
- "JARVIS, weather batao"
- "JARVIS, volume up karo"
- "JARVIS, search for Python tutorials"

System Status: Ready for commands
Language Support: Hindi + English (Mixed)
Voice Recognition: Active
"""
        self.response_text.insert("1.0", welcome_msg)
        
    def _create_system_monitor(self):
        """Create system monitoring display."""
        # This will be updated with real system data
        pass
        
    def _start_animations(self):
        """Start GUI animations."""
        self._animate_arc_reactor()
        self._update_system_stats()
        
    def _animate_arc_reactor(self):
        """Animate the arc reactor."""
        if not self.is_running:
            return
            
        # Clear canvas
        self.arc_reactor_canvas.delete("all")
        
        # Draw arc reactor
        center_x, center_y = 90, 90
        
        # Outer ring
        self.arc_reactor_canvas.create_oval(
            20, 20, 160, 160,
            outline=self.colors.get('primary', '#00D4FF'),
            width=3
        )
        
        # Inner rings with rotation
        for i in range(3):
            radius = 60 - i * 15
            angle_offset = self.arc_reactor_angle + i * 45
            
            for j in range(8):
                angle = math.radians(j * 45 + angle_offset)
                x1 = center_x + radius * math.cos(angle)
                y1 = center_y + radius * math.sin(angle)
                x2 = center_x + (radius - 10) * math.cos(angle)
                y2 = center_y + (radius - 10) * math.sin(angle)
                
                self.arc_reactor_canvas.create_line(
                    x1, y1, x2, y2,
                    fill=self.colors.get('primary', '#00D4FF'),
                    width=2
                )
                
        # Center core with pulsing
        pulse_radius = 15 + 5 * math.sin(self.pulse_intensity)
        self.arc_reactor_canvas.create_oval(
            center_x - pulse_radius, center_y - pulse_radius,
            center_x + pulse_radius, center_y + pulse_radius,
            fill=self.colors.get('primary', '#00D4FF'),
            outline=self.colors.get('secondary', '#FFD700'),
            width=2
        )
        
        # Update animation variables
        self.arc_reactor_angle += 2
        self.pulse_intensity += 0.2
        
        # Schedule next frame
        self.root.after(50, self._animate_arc_reactor)
        
    def _update_system_stats(self):
        """Update system statistics display."""
        if not self.is_running:
            return
            
        # This would get real system stats
        # For now, just update the display
        self.root.after(2000, self._update_system_stats)
        
    def _on_status_changed(self, status: str):
        """Handle status changes from JARVIS core."""
        status_colors = {
            'idle': self.colors.get('text', '#FFFFFF'),
            'listening': self.colors.get('primary', '#00D4FF'),
            'processing': self.colors.get('secondary', '#FFD700'),
            'speaking': self.colors.get('accent', '#FF6B6B'),
            'wake_word_detected': self.colors.get('primary', '#00D4FF')
        }
        
        status_texts = {
            'idle': '● IDLE',
            'listening': '● LISTENING',
            'processing': '● PROCESSING',
            'speaking': '● SPEAKING',
            'wake_word_detected': '● WAKE WORD DETECTED'
        }
        
        if self.status_label:
            self.status_label.configure(
                text=status_texts.get(status, f'● {status.upper()}'),
                text_color=status_colors.get(status, self.colors.get('text', '#FFFFFF'))
            )
            
    def _on_response_ready(self, response: str):
        """Handle response from JARVIS core."""
        if self.response_text:
            # Add timestamp
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            
            # Add response to text area
            self.response_text.insert("end", f"\n[{timestamp}] JARVIS: {response}\n")
            self.response_text.see("end")
            
    def _on_command_submit(self, event=None):
        """Handle command submission."""
        command = self.command_entry.get().strip()
        if command:
            # Clear entry
            self.command_entry.delete(0, "end")
            
            # Add command to response area
            import datetime
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            self.response_text.insert("end", f"\n[{timestamp}] User: {command}\n")
            
            # Process command asynchronously
            asyncio.create_task(self.jarvis_core.process_text_command(command))
            
    def _on_closing(self):
        """Handle window closing."""
        self.is_running = False
        self.root.quit()
        
    async def cleanup(self):
        """Clean up GUI resources."""
        self.is_running = False
        if self.root:
            self.root.quit()
            
        self.logger.info("✅ GUI cleanup complete")
