#!/usr/bin/env python3
"""
JARVIS Free Setup Script
Sets up JARVIS with completely FREE services - no paid APIs required!
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def print_banner():
    """Print setup banner."""
    print("🤖 JARVIS AI Assistant - FREE Setup")
    print("=" * 50)
    print("🎉 Setting up JARVIS with 100% FREE services!")
    print("💰 No paid APIs required - everything is free!")
    print("=" * 50)


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version}")


def install_free_dependencies():
    """Install only the free dependencies needed."""
    print("\n📦 Installing FREE dependencies...")
    
    # Minimal free dependencies
    free_deps = [
        "pyyaml",
        "python-dotenv", 
        "requests",
        "beautifulsoup4",  # For web scraping
        "feedparser",      # For RSS news feeds
        "customtkinter",   # For GUI
        "pillow",          # For images
    ]
    
    try:
        for dep in free_deps:
            print(f"   Installing {dep}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"   ✅ {dep} installed")
            
        print("✅ All free dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)


def setup_free_config():
    """Set up free configuration."""
    print("\n⚙️ Setting up FREE configuration...")
    
    # Copy free config
    free_env = Path(".env.free")
    env_file = Path(".env")
    
    if free_env.exists():
        import shutil
        shutil.copy(free_env, env_file)
        print("✅ Copied free configuration to .env")
    else:
        # Create minimal free config
        free_config = """# JARVIS FREE Configuration
USE_OFFLINE_AI=true
USE_SYSTEM_SPEECH=true
USE_DUCKDUCKGO_SEARCH=true
USE_RSS_NEWS=true
OPENWEATHER_API_KEY=
DEBUG=false
LOG_LEVEL=INFO
"""
        with open(env_file, "w") as f:
            f.write(free_config)
        print("✅ Created free configuration file")


def setup_directories():
    """Create necessary directories."""
    print("\n📁 Setting up directories...")
    
    directories = [
        "logs",
        "data", 
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")


def create_free_demo():
    """Create a demo script for free usage."""
    print("\n🎬 Creating free demo script...")
    
    demo_script = """#!/usr/bin/env python3
# JARVIS Free Demo - No APIs required!

import asyncio
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

async def free_demo():
    print("🤖 JARVIS FREE Demo")
    print("=" * 40)
    print("✅ Using 100% FREE services!")
    print("🗣️ System speech recognition")
    print("🔍 DuckDuckGo search (free)")
    print("📰 RSS news feeds (free)")
    print("🌤️ Weather (free tier)")
    print("🧠 Offline AI processing")
    print("=" * 40)
    
    # Test basic functionality
    commands = [
        "JARVIS, hello",
        "JARVIS, what time is it",
        "JARVIS, open Safari",
        "JARVIS, search for Python tutorials"
    ]
    
    print("\\n🎤 Testing voice commands:")
    for i, cmd in enumerate(commands, 1):
        print(f"{i}. {cmd}")
        print(f"   ✅ Would execute: {cmd.split(', ', 1)[1] if ', ' in cmd else cmd}")
    
    print("\\n🎉 JARVIS is ready with FREE services!")
    print("Run: python3 main.py")

if __name__ == "__main__":
    asyncio.run(free_demo())
"""
    
    with open("demo_free.py", "w") as f:
        f.write(demo_script)
    
    os.chmod("demo_free.py", 0o755)
    print("✅ Created demo_free.py")


def show_free_api_info():
    """Show information about free APIs."""
    print("\n🔑 Optional FREE API Keys:")
    print("-" * 30)
    
    free_apis = [
        {
            "name": "OpenWeatherMap",
            "url": "https://openweathermap.org/api",
            "free_tier": "1,000 calls/day",
            "description": "Weather information",
            "required": False
        },
        {
            "name": "NewsAPI", 
            "url": "https://newsapi.org/",
            "free_tier": "1,000 requests/day",
            "description": "News headlines",
            "required": False
        }
    ]
    
    for api in free_apis:
        print(f"\n📡 {api['name']}:")
        print(f"   🌐 URL: {api['url']}")
        print(f"   💰 Free tier: {api['free_tier']}")
        print(f"   📝 Use: {api['description']}")
        print(f"   ❓ Required: {'No' if not api['required'] else 'Yes'}")


def show_next_steps():
    """Show next steps."""
    print("\n" + "=" * 50)
    print("🎯 JARVIS FREE Setup Complete!")
    print("=" * 50)
    
    steps = [
        "1. 🚀 Start JARVIS:",
        "   python3 main.py",
        "",
        "2. 🎬 Try the free demo:",
        "   python3 demo_free.py",
        "",
        "3. 🎤 Test voice commands:",
        "   Say: 'JARVIS, hello'",
        "   Say: 'JARVIS, what time is it'",
        "",
        "4. 🔧 Optional improvements:",
        "   • Get free OpenWeatherMap API key for weather",
        "   • Add your email for email features",
        "",
        "5. 📚 Learn more:",
        "   • Check README.md for full documentation",
        "   • See .env file for configuration options"
    ]
    
    for step in steps:
        print(step)
    
    print("\n💡 Features working with FREE setup:")
    features = [
        "✅ Voice recognition (macOS built-in)",
        "✅ Text-to-speech (macOS built-in)", 
        "✅ System control (open apps, volume, etc.)",
        "✅ Web search (DuckDuckGo)",
        "✅ News (RSS feeds)",
        "✅ Time and basic info",
        "✅ Sci-fi GUI interface",
        "✅ Conversation memory"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("\n🎉 Enjoy your FREE JARVIS AI Assistant!")


def main():
    """Main setup function."""
    print_banner()
    
    # Check system requirements
    check_python_version()
    
    if platform.system() != "Darwin":
        print("⚠️ JARVIS is optimized for macOS. Some features may not work on other platforms.")
    
    # Install dependencies
    install_free_dependencies()
    
    # Setup project
    setup_directories()
    setup_free_config()
    create_free_demo()
    
    # Show information
    show_free_api_info()
    show_next_steps()


if __name__ == "__main__":
    main()
