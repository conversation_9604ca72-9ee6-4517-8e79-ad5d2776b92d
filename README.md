# JARVIS - AI Assistant

A sophisticated AI assistant inspired by <PERSON> Man's JARVIS, featuring:

- 🗣️ **Bilingual Speech**: Hindi and English speech recognition and synthesis
- 🖥️ **macOS Control**: Complete system control and automation
- 🌐 **Internet Access**: Web browsing, search, and online services
- 🚀 **Sci-Fi GUI**: Futuristic Iron Man-inspired interface
- 🧠 **Advanced AI**: Natural language processing and understanding

## Features

### Core Capabilities
- Voice commands in Hindi and English (mixed language support)
- Real-time speech recognition and natural voice synthesis
- Complete macOS system control (applications, files, settings)
- Internet browsing and web-based services
- Intelligent task automation
- Contextual conversations and memory

### Interface
- Futuristic holographic-style GUI
- Arc reactor-inspired visual elements
- Real-time voice visualization
- System status monitoring
- Interactive command interface

## Quick Start

### Prerequisites
- macOS 10.15+ (optimized for macOS)
- Python 3.8 or higher
- Microphone access
- Internet connection

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/your-username/jarvis.git
cd jarvis
```

2. **Run the setup script:**
```bash
python3 setup.py
```

3. **Configure API keys:**
```bash
# Edit .env file with your API keys
nano .env
```

Required API keys:
- `OPENAI_API_KEY` - For AI processing (required)
- `GOOGLE_SEARCH_API_KEY` - For web search (optional)
- `OPENWEATHER_API_KEY` - For weather info (optional)

4. **Start JARVIS:**
```bash
python3 main.py
# Or use the launch script:
./start_jarvis.sh
```

### First Run Setup

1. **Grant Permissions:**
   - Microphone access (for voice recognition)
   - Accessibility access (for system control)
   - Screen recording (for automation)

2. **Test Voice Recognition:**
   - Say "JARVIS" to activate
   - Try: "JARVIS, hello"

3. **Test System Control:**
   - "JARVIS, open Safari"
   - "JARVIS, volume up"

## Usage Guide

### Voice Commands

**System Control:**
```
English:
- "JARVIS, open Safari"
- "JARVIS, close all applications"
- "JARVIS, volume up"
- "JARVIS, set brightness to 50%"

Hindi:
- "JARVIS, music chalao" (play music)
- "JARVIS, volume kam karo" (lower volume)
- "JARVIS, Safari band karo" (close Safari)

Mixed:
- "JARVIS, WhatsApp open karo"
- "JARVIS, volume 80% set karo"
```

**Information & Search:**
```
- "JARVIS, weather batao" (tell weather)
- "JARVIS, search for Python tutorials"
- "JARVIS, what time is it?"
- "JARVIS, latest news dikhao" (show latest news)
```

**Web & Communication:**
```
- "JARVIS, open Google"
- "JARVIS, send email"
- "JARVIS, WhatsApp pe message bhejo"
- "JARVIS, search for restaurants near me"
```

### Text Commands

You can also type commands in the GUI interface:
- Type in the command box
- Press Enter or click Execute
- Supports same Hindi/English/Mixed syntax

### GUI Interface

The JARVIS interface includes:
- **Arc Reactor**: Central animated element showing system status
- **Voice Visualizer**: Real-time audio visualization
- **Command Input**: Text-based command interface
- **Response Panel**: JARVIS responses and conversation history
- **System Monitor**: CPU, memory, and network status

## Configuration

### Basic Configuration (`config.yaml`)

```yaml
# Speech settings
speech:
  wake_word: "jarvis"
  recognition:
    language: "hi-IN,en-US"
    timeout: 5
  synthesis:
    rate: 200
    volume: 0.8

# GUI settings
gui:
  theme: "arc_reactor"
  window_size: [1200, 800]
  transparency: 0.95

# System permissions
system:
  allowed_operations:
    - "open_application"
    - "volume_control"
    - "brightness_control"
```

### API Configuration (`.env`)

```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional but recommended
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
OPENWEATHER_API_KEY=your_weather_api_key
NEWS_API_KEY=your_news_api_key

# Email (for email features)
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_password
```

## Architecture

```
jarvis/
├── main.py                 # Main entry point
├── core/                   # Core AI and processing
│   ├── jarvis_core.py     # Main JARVIS engine
│   ├── nlp_processor.py   # Natural language processing
│   └── command_processor.py # Command execution
├── speech/                 # Speech recognition/synthesis
│   └── speech_manager.py  # Speech handling
├── system/                 # macOS system control
│   └── macos_controller.py # System automation
├── web/                   # Internet and web services
│   └── web_manager.py     # Web browsing and search
├── gui/                   # Graphical interface
│   └── main_interface.py  # Sci-fi GUI
├── utils/                 # Utilities and helpers
│   ├── config_manager.py  # Configuration management
│   ├── logger.py          # Logging system
│   └── memory_manager.py  # Conversation memory
└── config.yaml           # Main configuration
```

## Troubleshooting

### Common Issues

**1. "Permission denied" errors:**
```bash
# Grant accessibility permissions:
# System Preferences > Security & Privacy > Privacy > Accessibility
# Add Terminal and Python to the list
```

**2. Speech recognition not working:**
```bash
# Check microphone permissions:
# System Preferences > Security & Privacy > Privacy > Microphone
# Ensure Python/Terminal has microphone access
```

**3. "Module not found" errors:**
```bash
# Reinstall dependencies:
pip3 install -r requirements.txt

# Or use the setup script:
python3 setup.py
```

**4. API key errors:**
```bash
# Check .env file:
cat .env

# Ensure API keys are properly set
# No spaces around the = sign
```

### Testing

Run the test suite to verify installation:
```bash
python3 test_jarvis.py
```

### Logs

Check logs for debugging:
```bash
tail -f logs/jarvis.log
```

## Development

### Adding New Commands

1. **Add intent patterns** in `core/nlp_processor.py`
2. **Add command handler** in `core/command_processor.py`
3. **Test the new command** with voice or text input

### Customizing the GUI

1. **Modify colors** in `config.yaml`
2. **Add new components** in `gui/main_interface.py`
3. **Create custom themes** by extending the GUI class

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## API Keys Setup

### OpenAI API Key (Required)
1. Go to [OpenAI API](https://platform.openai.com/api-keys)
2. Create an account and generate an API key
3. Add to `.env`: `OPENAI_API_KEY=your_key_here`

### Google Search API (Optional)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Custom Search API
3. Create credentials
4. Add to `.env`: `GOOGLE_SEARCH_API_KEY=your_key_here`

### Weather API (Optional)
1. Go to [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for free API key
3. Add to `.env`: `OPENWEATHER_API_KEY=your_key_here`

## License

MIT License - See LICENSE file for details.

## Acknowledgments

- Inspired by Iron Man's JARVIS
- Built with Python and modern AI technologies
- Uses OpenAI for natural language processing
- Designed for macOS integration

## Support

For issues and questions:
- Check the [Issues](https://github.com/your-username/jarvis/issues) page
- Read the troubleshooting section above
- Check logs in `logs/jarvis.log`

---

**"Sometimes you gotta run before you can walk."** - Tony Stark
