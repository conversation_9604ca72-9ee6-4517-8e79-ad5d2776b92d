"""
Configuration Manager - Handles configuration loading and management
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv


class ConfigManager:
    """Manages configuration for JARVIS."""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = Path(config_path)
        self.config_data = {}
        self.env_vars = {}
        
        # Load configuration
        self._load_environment()
        self._load_config_file()
        
    def _load_environment(self):
        """Load environment variables."""
        # Load from .env file if it exists
        env_file = Path(".env")
        if env_file.exists():
            load_dotenv(env_file)
            
        # Store relevant environment variables
        env_keys = [
            'OPENAI_API_KEY',
            'ANTHROPIC_API_KEY',
            'GOOGLE_SPEECH_API_KEY',
            'AZURE_SPEECH_KEY',
            'AZURE_SPEECH_REGION',
            'GOOGLE_SEARCH_API_KEY',
            'GOOGLE_SEARCH_ENGINE_ID',
            'OPENWEATHER_API_KEY',
            'NEWS_API_KEY',
            'EMAIL_ADDRESS',
            'EMAIL_PASSWORD',
            'SMTP_SERVER',
            'SMTP_PORT',
            'DEBUG',
            'LOG_LEVEL'
        ]
        
        for key in env_keys:
            value = os.getenv(key)
            if value:
                self.env_vars[key.lower()] = value
                
    def _load_config_file(self):
        """Load configuration from YAML file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    self.config_data = yaml.safe_load(file) or {}
            else:
                logging.warning(f"Config file {self.config_path} not found, using defaults")
                self.config_data = self._get_default_config()
                
        except Exception as e:
            logging.error(f"Error loading config file: {e}")
            self.config_data = self._get_default_config()
            
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            'speech': {
                'recognition': {
                    'language': 'hi-IN,en-US',
                    'timeout': 5,
                    'phrase_timeout': 1,
                    'energy_threshold': 300
                },
                'synthesis': {
                    'voice_id': 'com.apple.speech.synthesis.voice.samantha',
                    'rate': 200,
                    'volume': 0.8,
                    'hindi_voice': 'com.apple.speech.synthesis.voice.lekha'
                },
                'wake_word': 'jarvis'
            },
            'ai': {
                'model': 'gpt-4',
                'max_tokens': 1000,
                'temperature': 0.7,
                'context_window': 10
            },
            'gui': {
                'theme': 'arc_reactor',
                'window_size': [1200, 800],
                'fullscreen': False,
                'transparency': 0.95,
                'animations': True,
                'colors': {
                    'primary': '#00D4FF',
                    'secondary': '#FFD700',
                    'background': '#0A0A0A',
                    'text': '#FFFFFF',
                    'accent': '#FF6B6B'
                }
            },
            'system': {
                'allowed_operations': [
                    'open_application',
                    'close_application',
                    'file_operations',
                    'system_settings',
                    'volume_control',
                    'brightness_control'
                ],
                'restricted_operations': [
                    'system_shutdown',
                    'delete_system_files'
                ]
            },
            'web': {
                'default_browser': 'Safari',
                'search_engine': 'https://www.google.com/search?q=',
                'user_agent': 'JARVIS/1.0'
            },
            'security': {
                'require_confirmation': True,
                'voice_authentication': False,
                'command_logging': True
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/jarvis.log',
                'max_size': '10MB',
                'backup_count': 5
            }
        }
        
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key."""
        # Check environment variables first
        if key in self.env_vars:
            return self.env_vars[key]
            
        # Check nested keys (e.g., 'speech.recognition.language')
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any):
        """Set configuration value."""
        keys = key.split('.')
        config = self.config_data
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # Set the value
        config[keys[-1]] = value
        
    def save(self):
        """Save configuration to file."""
        try:
            # Ensure directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(self.config_data, file, default_flow_style=False, indent=2)
                
        except Exception as e:
            logging.error(f"Error saving config file: {e}")
            
    def reload(self):
        """Reload configuration from file."""
        self._load_environment()
        self._load_config_file()
        
    def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for a specific service."""
        key_mapping = {
            'openai': 'openai_api_key',
            'anthropic': 'anthropic_api_key',
            'google_speech': 'google_speech_api_key',
            'azure_speech': 'azure_speech_key',
            'google_search': 'google_search_api_key',
            'openweather': 'openweather_api_key',
            'news': 'news_api_key'
        }
        
        key = key_mapping.get(service.lower())
        if key:
            return self.get(key)
        return None
        
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled."""
        debug = self.get('debug', 'false')
        return str(debug).lower() in ('true', '1', 'yes', 'on')
        
    def get_log_level(self) -> str:
        """Get logging level."""
        return self.get('log_level', 'INFO').upper()
        
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration and return any issues."""
        issues = {
            'missing_api_keys': [],
            'invalid_settings': [],
            'warnings': []
        }
        
        # Check for required API keys
        required_keys = ['openai_api_key']
        for key in required_keys:
            if not self.get(key):
                issues['missing_api_keys'].append(key)
                
        # Check optional but recommended keys
        recommended_keys = [
            'google_search_api_key',
            'openweather_api_key'
        ]
        for key in recommended_keys:
            if not self.get(key):
                issues['warnings'].append(f"Optional API key missing: {key}")
                
        # Validate GUI settings
        window_size = self.get('gui.window_size', [1200, 800])
        if not isinstance(window_size, list) or len(window_size) != 2:
            issues['invalid_settings'].append('gui.window_size must be [width, height]')
            
        transparency = self.get('gui.transparency', 0.95)
        if not 0.1 <= transparency <= 1.0:
            issues['invalid_settings'].append('gui.transparency must be between 0.1 and 1.0')
            
        return issues
