"""
Logger - Centralized logging configuration for JARVIS
"""

import logging
import logging.handlers
import os
from pathlib import Path
from typing import Optional


def setup_logging(config, logger_name: Optional[str] = None) -> logging.Logger:
    """Set up centralized logging for JARVIS."""
    
    # Get logging configuration
    log_config = config.get('logging', {})
    log_level = config.get_log_level()
    log_file = log_config.get('file', 'logs/jarvis.log')
    max_size = log_config.get('max_size', '10MB')
    backup_count = log_config.get('backup_count', 5)
    
    # Create logs directory
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Convert max_size to bytes
    size_multipliers = {'KB': 1024, 'MB': 1024**2, 'GB': 1024**3}
    max_bytes = 10 * 1024 * 1024  # Default 10MB
    
    if isinstance(max_size, str):
        for suffix, multiplier in size_multipliers.items():
            if max_size.upper().endswith(suffix):
                try:
                    size_value = float(max_size[:-2])
                    max_bytes = int(size_value * multiplier)
                except ValueError:
                    pass
                break
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level, logging.INFO))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s | %(name)s | %(levelname)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_level, logging.INFO))
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # Create logger for specific module if requested
    if logger_name:
        logger = logging.getLogger(logger_name)
    else:
        logger = logging.getLogger('jarvis')
    
    # Add custom log levels
    logging.addLevelName(25, 'SUCCESS')
    logging.addLevelName(35, 'IMPORTANT')
    
    def success(self, message, *args, **kwargs):
        if self.isEnabledFor(25):
            self._log(25, message, args, **kwargs)
    
    def important(self, message, *args, **kwargs):
        if self.isEnabledFor(35):
            self._log(35, message, args, **kwargs)
    
    logging.Logger.success = success
    logging.Logger.important = important
    
    # Log startup message
    logger.info("=" * 50)
    logger.info("🤖 JARVIS Logging System Initialized")
    logger.info(f"📁 Log file: {log_file}")
    logger.info(f"📊 Log level: {log_level}")
    logger.info(f"💾 Max file size: {max_size}")
    logger.info(f"🔄 Backup count: {backup_count}")
    logger.info("=" * 50)
    
    return logger


class JarvisLogFilter(logging.Filter):
    """Custom log filter for JARVIS-specific formatting."""
    
    def filter(self, record):
        # Add emoji prefixes based on log level
        emoji_map = {
            'DEBUG': '🔍',
            'INFO': 'ℹ️',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'CRITICAL': '💥',
            'SUCCESS': '✅',
            'IMPORTANT': '🔥'
        }
        
        emoji = emoji_map.get(record.levelname, '📝')
        record.emoji = emoji
        
        return True


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with JARVIS formatting."""
    logger = logging.getLogger(name)
    
    # Add custom filter if not already present
    if not any(isinstance(f, JarvisLogFilter) for f in logger.filters):
        logger.addFilter(JarvisLogFilter())
    
    return logger


def log_command_interaction(command: str, response: str, intent: dict, logger: logging.Logger):
    """Log command interactions for analysis."""
    try:
        interaction_data = {
            'command': command,
            'intent_action': intent.get('action', 'unknown'),
            'intent_category': intent.get('category', 'unknown'),
            'confidence': intent.get('confidence', 0.0),
            'language': intent.get('language', 'unknown'),
            'response_length': len(response),
            'timestamp': intent.get('timestamp', '')
        }
        
        logger.info(f"Command Interaction: {interaction_data}")
        
    except Exception as e:
        logger.error(f"Error logging command interaction: {e}")


def log_system_metrics(metrics: dict, logger: logging.Logger):
    """Log system performance metrics."""
    try:
        logger.debug(f"System Metrics: {metrics}")
    except Exception as e:
        logger.error(f"Error logging system metrics: {e}")


def log_error_with_context(error: Exception, context: dict, logger: logging.Logger):
    """Log errors with additional context."""
    try:
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context
        }
        
        logger.error(f"Error with context: {error_data}")
        
    except Exception as e:
        logger.error(f"Error logging error with context: {e}")


# Performance monitoring decorator
def log_performance(func):
    """Decorator to log function performance."""
    import time
    import functools
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = time.time()
        
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"⏱️ {func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"💥 {func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"⏱️ {func.__name__} executed in {execution_time:.3f}s")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"💥 {func.__name__} failed after {execution_time:.3f}s: {e}")
            raise
    
    # Return appropriate wrapper based on function type
    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper
