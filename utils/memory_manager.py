"""
Memory Manager - Handles conversation memory and context
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
import sqlite3
import threading


class MemoryManager:
    """Manages conversation memory and context for JARVIS."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Database setup
        self.db_path = Path("data/jarvis_memory.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Thread lock for database operations
        self.db_lock = threading.Lock()
        
        # In-memory cache
        self.recent_interactions = []
        self.user_preferences = {}
        self.context_cache = {}
        
        # Initialize database
        self._init_database()
        self._load_recent_interactions()
        
    def _init_database(self):
        """Initialize SQLite database for persistent memory."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Interactions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS interactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        command TEXT NOT NULL,
                        response TEXT NOT NULL,
                        intent_action TEXT,
                        intent_category TEXT,
                        confidence REAL,
                        language TEXT,
                        success BOOLEAN
                    )
                ''')
                
                # User preferences table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS preferences (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        updated_at TEXT NOT NULL
                    )
                ''')
                
                # Context table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS context (
                        session_id TEXT,
                        key TEXT,
                        value TEXT,
                        created_at TEXT,
                        expires_at TEXT,
                        PRIMARY KEY (session_id, key)
                    )
                ''')
                
                conn.commit()
                
            self.logger.info("✅ Memory database initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize memory database: {e}")
            
    def store_interaction(self, command: str, response: str, intent: Dict[str, Any]):
        """Store a command-response interaction."""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT INTO interactions 
                        (timestamp, command, response, intent_action, intent_category, 
                         confidence, language, success)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        datetime.now().isoformat(),
                        command,
                        response,
                        intent.get('action'),
                        intent.get('category'),
                        intent.get('confidence'),
                        intent.get('language'),
                        'error' not in response.lower()
                    ))
                    
                    conn.commit()
                    
            # Update in-memory cache
            interaction = {
                'timestamp': datetime.now(),
                'command': command,
                'response': response,
                'intent': intent
            }
            
            self.recent_interactions.append(interaction)
            
            # Keep only last 50 interactions in memory
            if len(self.recent_interactions) > 50:
                self.recent_interactions = self.recent_interactions[-50:]
                
            self.logger.debug(f"Stored interaction: {command[:50]}...")
            
        except Exception as e:
            self.logger.error(f"Error storing interaction: {e}")
            
    def get_recent_interactions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent interactions from memory."""
        return self.recent_interactions[-limit:]
        
    def search_interactions(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search interactions by command content."""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        SELECT timestamp, command, response, intent_action, intent_category
                        FROM interactions
                        WHERE command LIKE ? OR response LIKE ?
                        ORDER BY timestamp DESC
                        LIMIT ?
                    ''', (f'%{query}%', f'%{query}%', limit))
                    
                    results = []
                    for row in cursor.fetchall():
                        results.append({
                            'timestamp': row[0],
                            'command': row[1],
                            'response': row[2],
                            'intent_action': row[3],
                            'intent_category': row[4]
                        })
                        
                    return results
                    
        except Exception as e:
            self.logger.error(f"Error searching interactions: {e}")
            return []
            
    def set_preference(self, key: str, value: Any):
        """Set a user preference."""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO preferences (key, value, updated_at)
                        VALUES (?, ?, ?)
                    ''', (key, json.dumps(value), datetime.now().isoformat()))
                    
                    conn.commit()
                    
            # Update in-memory cache
            self.user_preferences[key] = value
            
            self.logger.debug(f"Set preference: {key} = {value}")
            
        except Exception as e:
            self.logger.error(f"Error setting preference: {e}")
            
    def get_preference(self, key: str, default: Any = None) -> Any:
        """Get a user preference."""
        try:
            # Check in-memory cache first
            if key in self.user_preferences:
                return self.user_preferences[key]
                
            # Check database
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        SELECT value FROM preferences WHERE key = ?
                    ''', (key,))
                    
                    result = cursor.fetchone()
                    if result:
                        value = json.loads(result[0])
                        self.user_preferences[key] = value
                        return value
                        
            return default
            
        except Exception as e:
            self.logger.error(f"Error getting preference: {e}")
            return default
            
    def set_context(self, session_id: str, key: str, value: Any, expires_in_hours: int = 24):
        """Set context information for a session."""
        try:
            expires_at = datetime.now() + timedelta(hours=expires_in_hours)
            
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT OR REPLACE INTO context 
                        (session_id, key, value, created_at, expires_at)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        session_id,
                        key,
                        json.dumps(value),
                        datetime.now().isoformat(),
                        expires_at.isoformat()
                    ))
                    
                    conn.commit()
                    
            # Update in-memory cache
            if session_id not in self.context_cache:
                self.context_cache[session_id] = {}
            self.context_cache[session_id][key] = value
            
        except Exception as e:
            self.logger.error(f"Error setting context: {e}")
            
    def get_context(self, session_id: str, key: str, default: Any = None) -> Any:
        """Get context information for a session."""
        try:
            # Check in-memory cache first
            if session_id in self.context_cache and key in self.context_cache[session_id]:
                return self.context_cache[session_id][key]
                
            # Check database
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        SELECT value FROM context 
                        WHERE session_id = ? AND key = ? AND expires_at > ?
                    ''', (session_id, key, datetime.now().isoformat()))
                    
                    result = cursor.fetchone()
                    if result:
                        value = json.loads(result[0])
                        
                        # Update cache
                        if session_id not in self.context_cache:
                            self.context_cache[session_id] = {}
                        self.context_cache[session_id][key] = value
                        
                        return value
                        
            return default
            
        except Exception as e:
            self.logger.error(f"Error getting context: {e}")
            return default
            
    def cleanup_expired_context(self):
        """Clean up expired context entries."""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        DELETE FROM context WHERE expires_at < ?
                    ''', (datetime.now().isoformat(),))
                    
                    deleted_count = cursor.rowcount
                    conn.commit()
                    
            if deleted_count > 0:
                self.logger.debug(f"Cleaned up {deleted_count} expired context entries")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up expired context: {e}")
            
    def get_conversation_summary(self, limit: int = 20) -> Dict[str, Any]:
        """Get a summary of recent conversation."""
        try:
            recent = self.get_recent_interactions(limit)
            
            if not recent:
                return {'total_interactions': 0}
                
            # Analyze patterns
            categories = {}
            languages = {}
            success_count = 0
            
            for interaction in recent:
                intent = interaction.get('intent', {})
                category = intent.get('category', 'unknown')
                language = intent.get('language', 'unknown')
                
                categories[category] = categories.get(category, 0) + 1
                languages[language] = languages.get(language, 0) + 1
                
                if 'error' not in interaction.get('response', '').lower():
                    success_count += 1
                    
            return {
                'total_interactions': len(recent),
                'success_rate': success_count / len(recent) if recent else 0,
                'top_categories': sorted(categories.items(), key=lambda x: x[1], reverse=True)[:3],
                'languages_used': languages,
                'time_span': {
                    'start': recent[0]['timestamp'].isoformat() if recent else None,
                    'end': recent[-1]['timestamp'].isoformat() if recent else None
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating conversation summary: {e}")
            return {'error': str(e)}
            
    def _load_recent_interactions(self):
        """Load recent interactions from database into memory."""
        try:
            with self.db_lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        SELECT timestamp, command, response, intent_action, 
                               intent_category, confidence, language
                        FROM interactions
                        ORDER BY timestamp DESC
                        LIMIT 50
                    ''')
                    
                    for row in cursor.fetchall():
                        interaction = {
                            'timestamp': datetime.fromisoformat(row[0]),
                            'command': row[1],
                            'response': row[2],
                            'intent': {
                                'action': row[3],
                                'category': row[4],
                                'confidence': row[5],
                                'language': row[6]
                            }
                        }
                        self.recent_interactions.append(interaction)
                        
            # Reverse to get chronological order
            self.recent_interactions.reverse()
            
            self.logger.debug(f"Loaded {len(self.recent_interactions)} recent interactions")
            
        except Exception as e:
            self.logger.error(f"Error loading recent interactions: {e}")
