"""
macOS Controller - System control and automation for macOS
"""

import asyncio
import logging
import subprocess
import os
import psutil
from typing import Dict, List, Optional, Any
import pyautogui
from pynput import mouse, keyboard


class MacOSController:
    """Controls macOS system functions and applications."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Security settings
        self.allowed_operations = config.get('system', {}).get('allowed_operations', [])
        self.restricted_operations = config.get('system', {}).get('restricted_operations', [])
        
        # Application mappings
        self.app_mappings = {
            'safari': 'Safari',
            'chrome': 'Google Chrome',
            'firefox': 'Firefox',
            'mail': 'Mail',
            'messages': 'Messages',
            'whatsapp': 'WhatsApp',
            'spotify': 'Spotify',
            'itunes': 'Music',
            'finder': 'Finder',
            'terminal': 'Terminal',
            'vscode': 'Visual Studio Code',
            'xcode': 'Xcode',
            'photoshop': 'Adobe Photoshop 2024',
            'illustrator': 'Adobe Illustrator 2024',
            'zoom': 'zoom.us',
            'teams': 'Microsoft Teams',
            'slack': 'Slack',
        }
        
    async def initialize(self):
        """Initialize macOS controller."""
        self.logger.info("Initializing macOS Controller...")
        
        try:
            # Check accessibility permissions
            if not self._check_accessibility_permissions():
                self.logger.warning("⚠️ Accessibility permissions may be required for full functionality")
                
            # Configure pyautogui
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 0.1
            
            self.logger.info("✅ macOS Controller initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize macOS Controller: {e}")
            raise
            
    def _check_accessibility_permissions(self) -> bool:
        """Check if accessibility permissions are granted."""
        try:
            # Try to get screen size (requires accessibility on some macOS versions)
            pyautogui.size()
            return True
        except Exception:
            return False
            
    async def open_application(self, app_name: str) -> Dict[str, Any]:
        """Open an application."""
        if 'open_application' not in self.allowed_operations:
            return {'success': False, 'error': 'Operation not allowed'}
            
        try:
            # Normalize app name
            app_name = app_name.lower().strip()
            actual_app_name = self.app_mappings.get(app_name, app_name.title())
            
            # Use AppleScript to open application
            script = f'''
            tell application "{actual_app_name}"
                activate
            end tell
            '''
            
            result = await self._run_applescript(script)
            
            if result['success']:
                self.logger.info(f"✅ Opened application: {actual_app_name}")
                return {
                    'success': True,
                    'message': f"एप्लिकेशन खोला गया। Opened {actual_app_name}",
                    'app_name': actual_app_name
                }
            else:
                return {
                    'success': False,
                    'error': f"Failed to open {actual_app_name}: {result.get('error', 'Unknown error')}"
                }
                
        except Exception as e:
            self.logger.error(f"Error opening application {app_name}: {e}")
            return {'success': False, 'error': str(e)}
            
    async def close_application(self, app_name: str) -> Dict[str, Any]:
        """Close an application."""
        if 'close_application' not in self.allowed_operations:
            return {'success': False, 'error': 'Operation not allowed'}
            
        try:
            app_name = app_name.lower().strip()
            actual_app_name = self.app_mappings.get(app_name, app_name.title())
            
            script = f'''
            tell application "{actual_app_name}"
                quit
            end tell
            '''
            
            result = await self._run_applescript(script)
            
            if result['success']:
                self.logger.info(f"✅ Closed application: {actual_app_name}")
                return {
                    'success': True,
                    'message': f"एप्लिकेशन बंद किया गया। Closed {actual_app_name}",
                    'app_name': actual_app_name
                }
            else:
                return {
                    'success': False,
                    'error': f"Failed to close {actual_app_name}: {result.get('error', 'Unknown error')}"
                }
                
        except Exception as e:
            self.logger.error(f"Error closing application {app_name}: {e}")
            return {'success': False, 'error': str(e)}
            
    async def control_volume(self, action: str, value: Optional[int] = None) -> Dict[str, Any]:
        """Control system volume."""
        if 'volume_control' not in self.allowed_operations:
            return {'success': False, 'error': 'Operation not allowed'}
            
        try:
            if action == 'set' and value is not None:
                # Set volume to specific value (0-100)
                volume = max(0, min(100, value))
                script = f'set volume output volume {volume}'
                
            elif action == 'up':
                script = 'set volume output volume (output volume of (get volume settings) + 10)'
                
            elif action == 'down':
                script = 'set volume output volume (output volume of (get volume settings) - 10)'
                
            elif action == 'mute':
                script = 'set volume with output muted'
                
            elif action == 'unmute':
                script = 'set volume without output muted'
                
            else:
                return {'success': False, 'error': 'Invalid volume action'}
                
            result = await self._run_applescript(script)
            
            if result['success']:
                # Get current volume
                volume_script = 'output volume of (get volume settings)'
                volume_result = await self._run_applescript(volume_script)
                current_volume = volume_result.get('output', '50')
                
                return {
                    'success': True,
                    'message': f"वॉल्यूम सेट किया गया। Volume set to {current_volume}%",
                    'volume': current_volume
                }
            else:
                return {'success': False, 'error': result.get('error', 'Failed to control volume')}
                
        except Exception as e:
            self.logger.error(f"Error controlling volume: {e}")
            return {'success': False, 'error': str(e)}
            
    async def control_brightness(self, action: str, value: Optional[int] = None) -> Dict[str, Any]:
        """Control screen brightness."""
        if 'brightness_control' not in self.allowed_operations:
            return {'success': False, 'error': 'Operation not allowed'}
            
        try:
            if action == 'set' and value is not None:
                # Set brightness (0.0 to 1.0)
                brightness = max(0, min(100, value)) / 100.0
                cmd = f"brightness {brightness}"
                
            elif action == 'up':
                cmd = "brightness -l | grep 'display 0' | awk '{print $4}' | xargs -I {} brightness $(echo '{} + 0.1' | bc)"
                
            elif action == 'down':
                cmd = "brightness -l | grep 'display 0' | awk '{print $4}' | xargs -I {} brightness $(echo '{} - 0.1' | bc)"
                
            else:
                return {'success': False, 'error': 'Invalid brightness action'}
                
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                return {
                    'success': True,
                    'message': f"ब्राइटनेस सेट की गई। Brightness adjusted",
                }
            else:
                return {'success': False, 'error': 'Failed to control brightness'}
                
        except Exception as e:
            self.logger.error(f"Error controlling brightness: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Battery info (if available)
            battery = None
            try:
                battery_info = psutil.sensors_battery()
                if battery_info:
                    battery = {
                        'percent': battery_info.percent,
                        'plugged': battery_info.power_plugged
                    }
            except:
                pass
                
            # Running applications
            running_apps = []
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] and not proc.info['name'].startswith('.'):
                        running_apps.append(proc.info['name'])
                except:
                    continue
                    
            return {
                'success': True,
                'cpu_usage': cpu_percent,
                'memory_usage': memory_percent,
                'disk_usage': disk_percent,
                'battery': battery,
                'running_apps': list(set(running_apps))[:20]  # Limit to 20 unique apps
            }
            
        except Exception as e:
            self.logger.error(f"Error getting system info: {e}")
            return {'success': False, 'error': str(e)}
            
    async def file_operations(self, operation: str, path: str, destination: str = None) -> Dict[str, Any]:
        """Perform file operations."""
        if 'file_operations' not in self.allowed_operations:
            return {'success': False, 'error': 'Operation not allowed'}
            
        try:
            if operation == 'open':
                script = f'tell application "Finder" to open POSIX file "{path}"'
                
            elif operation == 'reveal':
                script = f'tell application "Finder" to reveal POSIX file "{path}"'
                
            elif operation == 'delete':
                script = f'tell application "Finder" to delete POSIX file "{path}"'
                
            elif operation == 'copy' and destination:
                script = f'''
                tell application "Finder"
                    copy POSIX file "{path}" to POSIX file "{destination}"
                end tell
                '''
                
            else:
                return {'success': False, 'error': 'Invalid file operation'}
                
            result = await self._run_applescript(script)
            
            if result['success']:
                return {
                    'success': True,
                    'message': f"फाइल ऑपरेशन पूरा हुआ। File operation completed: {operation}",
                    'operation': operation,
                    'path': path
                }
            else:
                return {'success': False, 'error': result.get('error', 'File operation failed')}
                
        except Exception as e:
            self.logger.error(f"Error in file operation: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _run_applescript(self, script: str) -> Dict[str, Any]:
        """Run AppleScript command."""
        try:
            result = subprocess.run(
                ['osascript', '-e', script],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return {
                    'success': True,
                    'output': result.stdout.strip()
                }
            else:
                return {
                    'success': False,
                    'error': result.stderr.strip()
                }
                
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': 'AppleScript timeout'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
    async def shutdown(self):
        """Shutdown macOS controller."""
        self.logger.info("✅ macOS Controller shutdown complete")
