#!/usr/bin/env python3
"""
JARVIS Test Suite
Basic tests to verify JARVIS functionality
"""

import asyncio
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from utils.config_manager import ConfigManager
from utils.logger import setup_logging
from core.nlp_processor import NLPProcessor
from speech.speech_manager import SpeechManager
from system.macos_controller import MacOSController
from web.web_manager import WebManager


class JarvisTestSuite:
    """Test suite for JARVIS components."""
    
    def __init__(self):
        self.config = ConfigManager()
        self.logger = setup_logging(self.config, "test")
        self.test_results = {}
        
    async def run_all_tests(self):
        """Run all tests."""
        print("🧪 JARVIS Test Suite")
        print("=" * 50)
        
        tests = [
            ("Configuration", self.test_configuration),
            ("NLP Processor", self.test_nlp_processor),
            ("Speech Manager", self.test_speech_manager),
            ("macOS Controller", self.test_macos_controller),
            ("Web Manager", self.test_web_manager),
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 Testing {test_name}...")
            try:
                result = await test_func()
                self.test_results[test_name] = result
                if result:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"💥 {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
                
        self.print_summary()
        
    def test_configuration(self):
        """Test configuration loading."""
        try:
            # Test basic config access
            speech_config = self.config.get('speech', {})
            gui_config = self.config.get('gui', {})
            
            # Test validation
            issues = self.config.validate_config()
            
            print(f"   📋 Config validation issues: {len(issues['missing_api_keys'])} missing keys")
            
            return True
        except Exception as e:
            print(f"   ❌ Configuration error: {e}")
            return False
            
    async def test_nlp_processor(self):
        """Test NLP processor."""
        try:
            nlp = NLPProcessor(self.config)
            await nlp.initialize()
            
            # Test intent analysis
            test_commands = [
                "JARVIS open Safari",
                "volume up karo",
                "weather batao Delhi mein",
                "search for Python tutorials"
            ]
            
            for command in test_commands:
                intent = await nlp.analyze_intent(command)
                print(f"   🗣️ '{command}' -> {intent['action']} ({intent['category']})")
                
            return True
        except Exception as e:
            print(f"   ❌ NLP error: {e}")
            return False
            
    async def test_speech_manager(self):
        """Test speech manager initialization."""
        try:
            speech = SpeechManager(self.config)
            await speech.initialize()
            
            # Test TTS (without actually speaking)
            print("   🎤 Speech recognition initialized")
            print("   🔊 Text-to-speech initialized")
            
            await speech.shutdown()
            return True
        except Exception as e:
            print(f"   ❌ Speech error: {e}")
            return False
            
    async def test_macos_controller(self):
        """Test macOS controller."""
        try:
            controller = MacOSController(self.config)
            await controller.initialize()
            
            # Test system info
            info = await controller.get_system_info()
            if info.get('success'):
                print(f"   💻 CPU: {info['cpu_usage']:.1f}%")
                print(f"   🧠 Memory: {info['memory_usage']:.1f}%")
                print(f"   💾 Disk: {info['disk_usage']:.1f}%")
            
            await controller.shutdown()
            return True
        except Exception as e:
            print(f"   ❌ macOS Controller error: {e}")
            return False
            
    async def test_web_manager(self):
        """Test web manager."""
        try:
            web = WebManager(self.config)
            await web.initialize()
            
            # Test basic functionality (without making actual requests)
            print("   🌐 Web manager initialized")
            print("   🔍 Search capabilities ready")
            
            await web.shutdown()
            return True
        except Exception as e:
            print(f"   ❌ Web Manager error: {e}")
            return False
            
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)
        
        passed = sum(1 for result in self.test_results.values() if result)
        total = len(self.test_results)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name:<20} {status}")
            
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! JARVIS is ready to run.")
        else:
            print("⚠️ Some tests failed. Check the errors above.")
            
        return passed == total


async def main():
    """Run the test suite."""
    test_suite = JarvisTestSuite()
    await test_suite.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
