"""
Command Processor - Executes commands based on analyzed intent
"""

import asyncio
import logging
from typing import Dict, Any, Optional
import re


class CommandProcessor:
    """Processes and executes commands based on intent analysis."""
    
    def __init__(self, config, macos_controller, web_manager, speech_manager):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Component references
        self.macos_controller = macos_controller
        self.web_manager = web_manager
        self.speech_manager = speech_manager
        
        # Command handlers mapping
        self.command_handlers = {
            'system_control': self._handle_system_control,
            'web_search': self._handle_web_search,
            'media_control': self._handle_media_control,
            'information': self._handle_information,
            'communication': self._handle_communication,
            'file_operations': self._handle_file_operations,
            'general': self._handle_general
        }
        
    async def execute_command(self, intent: Dict[str, Any]) -> str:
        """Execute a command based on analyzed intent."""
        try:
            category = intent.get('category', 'general')
            action = intent.get('action', 'unknown')
            target = intent.get('target', 'unknown')
            
            self.logger.info(f"Executing command: {category}.{action} -> {target}")
            
            # Get appropriate handler
            handler = self.command_handlers.get(category, self._handle_general)
            
            # Execute command
            result = await handler(intent)
            
            # Generate response
            if isinstance(result, dict) and result.get('success'):
                return result.get('message', 'Command executed successfully.')
            elif isinstance(result, dict):
                return result.get('error', 'Command failed.')
            else:
                return str(result)
                
        except Exception as e:
            self.logger.error(f"Error executing command: {e}")
            return f"माफ करें, कमांड execute करने में समस्या हुई। Sorry, there was an error executing the command: {str(e)}"
            
    async def _handle_system_control(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system control commands."""
        action = intent.get('action')
        target = intent.get('target')
        entities = intent.get('entities', {})
        
        if action == 'open':
            return await self.macos_controller.open_application(target)
            
        elif action == 'close':
            return await self.macos_controller.close_application(target)
            
        elif action in ['set', 'up', 'down'] and 'volume' in target:
            # Extract volume value if present
            numbers = entities.get('numbers', [])
            value = int(numbers[0]) if numbers else None
            return await self.macos_controller.control_volume(action, value)
            
        elif action in ['set', 'up', 'down'] and 'brightness' in target:
            numbers = entities.get('numbers', [])
            value = int(numbers[0]) if numbers else None
            return await self.macos_controller.control_brightness(action, value)
            
        elif action == 'get' and target == 'system':
            return await self.macos_controller.get_system_info()
            
        else:
            return {
                'success': False,
                'error': f'Unknown system command: {action} {target}'
            }
            
    async def _handle_web_search(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle web search commands."""
        action = intent.get('action')
        original_text = intent.get('original_text', '')
        
        if action == 'search':
            # Extract search query from original text
            query = self._extract_search_query(original_text)
            
            if query:
                result = await self.web_manager.search_web(query, num_results=3)
                
                if result.get('success'):
                    results = result.get('results', [])
                    if results:
                        response = f"खोज के परिणाम मिले। Found {len(results)} search results for '{query}':\n\n"
                        for i, item in enumerate(results[:3], 1):
                            response += f"{i}. {item.get('title', 'No title')}\n"
                            response += f"   {item.get('snippet', 'No description')}\n\n"
                        return {'success': True, 'message': response}
                    else:
                        return {'success': False, 'error': f'No results found for "{query}"'}
                else:
                    return result
            else:
                return {'success': False, 'error': 'Could not extract search query'}
                
        elif action == 'open':
            # Open website
            websites = intent.get('entities', {}).get('websites', [])
            if websites:
                return await self.web_manager.open_website(websites[0])
            else:
                return {'success': False, 'error': 'No website specified'}
                
        return {'success': False, 'error': 'Unknown web command'}
        
    async def _handle_media_control(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle media control commands."""
        action = intent.get('action')
        target = intent.get('target')
        
        if action == 'play':
            if 'music' in target or 'spotify' in target:
                return await self.macos_controller.open_application('spotify')
            elif 'video' in target:
                return await self.macos_controller.open_application('quicktime player')
            else:
                return await self.macos_controller.open_application('music')
                
        elif action == 'stop':
            # Use system volume control or close media apps
            return await self.macos_controller.control_volume('mute')
            
        return {'success': False, 'error': 'Unknown media command'}
        
    async def _handle_information(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle information requests."""
        action = intent.get('action')
        target = intent.get('target')
        
        if 'weather' in target:
            # Extract location if mentioned
            original_text = intent.get('original_text', '')
            location = self._extract_location(original_text)
            result = await self.web_manager.get_weather(location)
            return result
            
        elif 'time' in target:
            import datetime
            now = datetime.datetime.now()
            time_str = now.strftime("%I:%M %p")
            date_str = now.strftime("%A, %B %d, %Y")
            
            return {
                'success': True,
                'message': f"समय है {time_str}। Current time is {time_str} on {date_str}"
            }
            
        elif 'news' in target:
            result = await self.web_manager.get_news()
            if result.get('success'):
                articles = result.get('articles', [])
                if articles:
                    response = "ताज़ा समाचार। Latest news headlines:\n\n"
                    for i, article in enumerate(articles[:3], 1):
                        response += f"{i}. {article.get('title', 'No title')}\n"
                        if article.get('description'):
                            response += f"   {article['description'][:100]}...\n\n"
                    return {'success': True, 'message': response}
            return result
            
        return {'success': False, 'error': 'Unknown information request'}
        
    async def _handle_communication(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle communication commands."""
        action = intent.get('action')
        target = intent.get('target')
        
        if action == 'send' and 'email' in target:
            return await self.macos_controller.open_application('mail')
            
        elif action == 'send' and 'message' in target:
            return await self.macos_controller.open_application('messages')
            
        elif action == 'call':
            # Open FaceTime or similar
            return await self.macos_controller.open_application('facetime')
            
        return {'success': False, 'error': 'Unknown communication command'}
        
    async def _handle_file_operations(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file operations."""
        action = intent.get('action')
        
        if action in ['create', 'open', 'delete']:
            # Open Finder for file operations
            return await self.macos_controller.open_application('finder')
            
        return {'success': False, 'error': 'Unknown file operation'}
        
    async def _handle_general(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general commands and conversations."""
        original_text = intent.get('original_text', '').lower()
        
        # Greeting responses
        greetings = ['hello', 'hi', 'hey', 'namaste', 'namaskar', 'हैलो', 'नमस्ते']
        if any(greeting in original_text for greeting in greetings):
            return {
                'success': True,
                'message': 'नमस्ते! Hello! I am JARVIS, your AI assistant. How can I help you today?'
            }
            
        # Thank you responses
        thanks = ['thank', 'thanks', 'धन्यवाद', 'शुक्रिया']
        if any(thank in original_text for thank in thanks):
            return {
                'success': True,
                'message': 'आपका स्वागत है! You are welcome! Happy to help.'
            }
            
        # Status check
        if 'how are you' in original_text or 'कैसे हो' in original_text:
            return {
                'success': True,
                'message': 'मैं बिल्कुल ठीक हूँ! I am functioning perfectly and ready to assist you.'
            }
            
        # Default response for unrecognized commands
        return {
            'success': True,
            'message': 'मुझे समझ नहीं आया। I did not understand that command. Could you please rephrase or try a different command?'
        }
        
    def _extract_search_query(self, text: str) -> str:
        """Extract search query from command text."""
        # Remove common command words
        remove_words = [
            'jarvis', 'search', 'find', 'look', 'google', 'खोजो', 'ढूंढो',
            'for', 'about', 'on', 'के', 'बारे', 'में'
        ]
        
        words = text.lower().split()
        filtered_words = [word for word in words if word not in remove_words]
        
        return ' '.join(filtered_words).strip()
        
    def _extract_location(self, text: str) -> str:
        """Extract location from text."""
        # Simple location extraction
        text = text.lower()
        
        # Common location indicators
        location_indicators = ['in', 'at', 'for', 'में', 'का', 'की']
        
        for indicator in location_indicators:
            if indicator in text:
                parts = text.split(indicator)
                if len(parts) > 1:
                    potential_location = parts[-1].strip()
                    # Clean up the location
                    words = potential_location.split()[:2]  # Take first 2 words
                    return ' '.join(words)
                    
        return 'current'  # Default to current location
