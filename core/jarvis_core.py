"""
JARVIS Core - Main AI processing engine
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Any
from datetime import datetime

from speech.speech_manager import SpeechManager
from system.macos_controller import MacOSController
from web.web_manager import WebManager
from core.nlp_processor import NLPProcessor
from core.command_processor import CommandProcessor
from utils.memory_manager import MemoryManager


class JarvisCore:
    """Core JARVIS AI system."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.speech_manager = None
        self.macos_controller = None
        self.web_manager = None
        self.nlp_processor = None
        self.command_processor = None
        self.memory_manager = None
        
        # State
        self.is_listening = False
        self.is_processing = False
        self.conversation_context = []
        
        # Event callbacks
        self.on_command_received = None
        self.on_response_ready = None
        self.on_status_changed = None
        
    async def initialize(self):
        """Initialize all JARVIS components."""
        self.logger.info("Initializing JARVIS Core...")
        
        try:
            # Initialize memory manager
            self.memory_manager = MemoryManager(self.config)
            
            # Initialize NLP processor
            self.nlp_processor = NLPProcessor(self.config)
            await self.nlp_processor.initialize()
            
            # Initialize speech manager
            self.speech_manager = SpeechManager(self.config)
            await self.speech_manager.initialize()
            
            # Initialize system controller
            self.macos_controller = MacOSController(self.config)
            await self.macos_controller.initialize()
            
            # Initialize web manager
            self.web_manager = WebManager(self.config)
            await self.web_manager.initialize()
            
            # Initialize command processor
            self.command_processor = CommandProcessor(
                self.config,
                self.macos_controller,
                self.web_manager,
                self.speech_manager
            )
            
            # Set up speech callbacks
            self.speech_manager.on_wake_word_detected = self._on_wake_word
            self.speech_manager.on_command_recognized = self._on_command_recognized
            
            self.logger.info("✅ JARVIS Core initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize JARVIS Core: {e}")
            raise
            
    async def start_listening(self):
        """Start listening for voice commands."""
        if not self.is_listening:
            self.is_listening = True
            await self.speech_manager.start_listening()
            self._notify_status_change("listening")
            self.logger.info("🎤 JARVIS is now listening...")
            
    async def stop_listening(self):
        """Stop listening for voice commands."""
        if self.is_listening:
            self.is_listening = False
            await self.speech_manager.stop_listening()
            self._notify_status_change("idle")
            self.logger.info("🔇 JARVIS stopped listening")
            
    async def process_text_command(self, text: str) -> str:
        """Process a text command directly."""
        return await self._process_command(text, source="text")
        
    async def _on_wake_word(self):
        """Handle wake word detection."""
        self.logger.info("👂 Wake word detected!")
        await self.speech_manager.speak("हाँ, मैं सुन रहा हूँ। Yes, I'm listening.")
        self._notify_status_change("wake_word_detected")
        
    async def _on_command_recognized(self, command: str, confidence: float):
        """Handle recognized voice command."""
        self.logger.info(f"🗣️ Command recognized: '{command}' (confidence: {confidence:.2f})")
        
        if confidence < 0.6:
            await self.speech_manager.speak("मुझे समझ नहीं आया। Could you repeat that?")
            return
            
        response = await self._process_command(command, source="voice")
        if response:
            await self.speech_manager.speak(response)
            
    async def _process_command(self, command: str, source: str = "voice") -> str:
        """Process a command and return response."""
        if self.is_processing:
            return "मैं अभी व्यस्त हूँ। I'm currently busy processing another command."
            
        self.is_processing = True
        self._notify_status_change("processing")
        
        try:
            # Add to conversation context
            self.conversation_context.append({
                "timestamp": datetime.now(),
                "command": command,
                "source": source
            })
            
            # Keep only last 10 commands for context
            if len(self.conversation_context) > 10:
                self.conversation_context = self.conversation_context[-10:]
                
            # Process with NLP
            intent = await self.nlp_processor.analyze_intent(command, self.conversation_context)
            
            # Execute command
            response = await self.command_processor.execute_command(intent)
            
            # Store in memory
            self.memory_manager.store_interaction(command, response, intent)
            
            if self.on_response_ready:
                self.on_response_ready(response)
                
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing command: {e}")
            return f"माफ करें, कुछ गलत हुआ है। Sorry, something went wrong: {str(e)}"
            
        finally:
            self.is_processing = False
            self._notify_status_change("idle" if not self.is_listening else "listening")
            
    def _notify_status_change(self, status: str):
        """Notify status change to GUI."""
        if self.on_status_changed:
            self.on_status_changed(status)
            
    async def shutdown(self):
        """Shutdown JARVIS core."""
        self.logger.info("Shutting down JARVIS Core...")
        
        if self.speech_manager:
            await self.speech_manager.shutdown()
        if self.macos_controller:
            await self.macos_controller.shutdown()
        if self.web_manager:
            await self.web_manager.shutdown()
            
        self.logger.info("✅ JARVIS Core shutdown complete")
