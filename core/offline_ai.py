"""
Offline AI Processor - No external APIs required
Provides basic AI responses using pattern matching and templates
"""

import re
import random
import logging
from typing import Dict, List, Any
from datetime import datetime


class OfflineAI:
    """Offline AI processor for basic responses."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Response templates
        self.responses = {
            'greeting': [
                "नमस्ते! Hello! I am <PERSON><PERSON><PERSON><PERSON>, your AI assistant. How can I help you?",
                "Hello! JARVIS at your service. What can I do for you today?",
                "नमस्कार! Hi there! I'm ready to assist you.",
                "Greetings! JARVIS here, ready to help with your tasks."
            ],
            'thanks': [
                "आपका स्वागत है! You're welcome! Happy to help.",
                "My pleasure! That's what I'm here for.",
                "खुशी हुई! Glad I could assist you.",
                "Anytime! Feel free to ask for more help."
            ],
            'status': [
                "मैं बिल्कुल ठीक हूँ! I'm functioning perfectly and ready to assist.",
                "All systems operational! How can I help you today?",
                "सब कुछ ठीक है! Everything is working great!",
                "I'm doing excellent! Ready for your commands."
            ],
            'unknown': [
                "मुझे समझ नहीं आया। I didn't understand that. Could you rephrase?",
                "I'm not sure about that. Can you try a different command?",
                "समझ नहीं आया। Could you please be more specific?",
                "I need more information to help you with that."
            ],
            'system_success': [
                "कमांड execute हो गया! Command executed successfully.",
                "Done! The task has been completed.",
                "हो गया! Task completed successfully.",
                "Success! Your request has been processed."
            ],
            'system_error': [
                "माफ करें, कुछ गलत हुआ। Sorry, something went wrong.",
                "I encountered an error while processing that command.",
                "समस्या हुई। There was an issue with that request.",
                "Oops! Something didn't work as expected."
            ]
        }
        
        # Intent patterns
        self.intent_patterns = {
            'greeting': [
                r'\b(hello|hi|hey|namaste|namaskar|हैलो|नमस्ते)\b',
                r'\b(good morning|good afternoon|good evening)\b'
            ],
            'thanks': [
                r'\b(thank|thanks|धन्यवाद|शुक्रिया)\b'
            ],
            'status': [
                r'\b(how are you|कैसे हो|कैसी हो)\b',
                r'\b(status|health|condition)\b'
            ],
            'time': [
                r'\b(time|clock|समय|वक्त)\b',
                r'\b(what time|current time)\b'
            ],
            'weather': [
                r'\b(weather|मौसम|climate)\b',
                r'\b(temperature|rain|sunny)\b'
            ],
            'search': [
                r'\b(search|find|look|खोजो|ढूंढो)\b',
                r'\b(google|bing|duckduckgo)\b'
            ],
            'open': [
                r'\b(open|launch|start|खोलो|चालू)\b',
                r'\b(application|app|program)\b'
            ],
            'close': [
                r'\b(close|quit|exit|बंद|band)\b',
                r'\b(shutdown|stop)\b'
            ],
            'volume': [
                r'\b(volume|sound|audio|आवाज़|आवाज)\b',
                r'\b(loud|quiet|mute)\b'
            ],
            'brightness': [
                r'\b(brightness|screen|display|रोशनी)\b',
                r'\b(bright|dim|dark)\b'
            ]
        }
        
    async def process_command(self, text: str, intent: Dict[str, Any]) -> str:
        """Process command and generate response."""
        try:
            category = intent.get('category', 'general')
            action = intent.get('action', 'unknown')
            
            # Handle different categories
            if category == 'general':
                return self._handle_general(text, intent)
            elif category == 'system_control':
                return self._handle_system_control(text, intent)
            elif category == 'information':
                return self._handle_information(text, intent)
            elif category == 'web_search':
                return self._handle_web_search(text, intent)
            else:
                return self._get_response('unknown')
                
        except Exception as e:
            self.logger.error(f"Error processing command: {e}")
            return self._get_response('system_error')
            
    def _handle_general(self, text: str, intent: Dict[str, Any]) -> str:
        """Handle general conversation."""
        text_lower = text.lower()
        
        # Check for greeting patterns
        for pattern in self.intent_patterns['greeting']:
            if re.search(pattern, text_lower):
                return self._get_response('greeting')
                
        # Check for thanks patterns
        for pattern in self.intent_patterns['thanks']:
            if re.search(pattern, text_lower):
                return self._get_response('thanks')
                
        # Check for status patterns
        for pattern in self.intent_patterns['status']:
            if re.search(pattern, text_lower):
                return self._get_response('status')
                
        return self._get_response('unknown')
        
    def _handle_system_control(self, text: str, intent: Dict[str, Any]) -> str:
        """Handle system control commands."""
        action = intent.get('action', 'unknown')
        target = intent.get('target', 'unknown')
        
        if action == 'open':
            return f"Opening {target}. {self._get_response('system_success')}"
        elif action == 'close':
            return f"Closing {target}. {self._get_response('system_success')}"
        elif action in ['up', 'down', 'set'] and 'volume' in target:
            return f"Adjusting volume. {self._get_response('system_success')}"
        elif action in ['up', 'down', 'set'] and 'brightness' in target:
            return f"Adjusting brightness. {self._get_response('system_success')}"
        else:
            return f"System command: {action} {target}. {self._get_response('system_success')}"
            
    def _handle_information(self, text: str, intent: Dict[str, Any]) -> str:
        """Handle information requests."""
        target = intent.get('target', 'unknown')
        
        if 'time' in target:
            now = datetime.now()
            time_str = now.strftime("%I:%M %p")
            date_str = now.strftime("%A, %B %d, %Y")
            return f"समय है {time_str}। Current time is {time_str} on {date_str}"
            
        elif 'weather' in target:
            return "मौसम की जानकारी के लिए OpenWeatherMap API key चाहिए। For weather information, please add OpenWeatherMap API key to your .env file."
            
        elif 'news' in target:
            return "समाचार के लिए RSS feeds का उपयोग कर रहे हैं। Getting news from RSS feeds. Please check the web interface for latest updates."
            
        else:
            return f"Information about {target}: {self._get_response('unknown')}"
            
    def _handle_web_search(self, text: str, intent: Dict[str, Any]) -> str:
        """Handle web search commands."""
        # Extract search query
        query = self._extract_search_query(text)
        
        if query:
            return f"Searching for '{query}' using DuckDuckGo. {self._get_response('system_success')}"
        else:
            return "कृपया search query बताएं। Please specify what you want to search for."
            
    def _extract_search_query(self, text: str) -> str:
        """Extract search query from text."""
        # Remove common command words
        remove_words = [
            'jarvis', 'search', 'find', 'look', 'google', 'खोजो', 'ढूंढो',
            'for', 'about', 'on', 'के', 'बारे', 'में'
        ]
        
        words = text.lower().split()
        filtered_words = [word for word in words if word not in remove_words]
        
        return ' '.join(filtered_words).strip()
        
    def _get_response(self, response_type: str) -> str:
        """Get a random response of the specified type."""
        responses = self.responses.get(response_type, self.responses['unknown'])
        return random.choice(responses)
        
    def analyze_intent_offline(self, text: str) -> Dict[str, Any]:
        """Analyze intent using offline pattern matching."""
        try:
            text_lower = text.lower()
            
            # Default intent
            intent = {
                'action': 'unknown',
                'target': 'unknown',
                'category': 'general',
                'confidence': 0.5,
                'language': self._detect_language(text),
                'original_text': text,
                'timestamp': datetime.now().isoformat(),
                'entities': {}
            }
            
            # Check for system control patterns
            if self._matches_patterns(text_lower, ['open', 'close', 'volume', 'brightness']):
                intent['category'] = 'system_control'
                intent['confidence'] = 0.8
                
                if self._matches_patterns(text_lower, ['open']):
                    intent['action'] = 'open'
                    intent['target'] = self._extract_target(text, 'open')
                elif self._matches_patterns(text_lower, ['close']):
                    intent['action'] = 'close'
                    intent['target'] = self._extract_target(text, 'close')
                elif self._matches_patterns(text_lower, ['volume']):
                    intent['action'] = 'set'
                    intent['target'] = 'volume'
                elif self._matches_patterns(text_lower, ['brightness']):
                    intent['action'] = 'set'
                    intent['target'] = 'brightness'
                    
            # Check for information patterns
            elif self._matches_patterns(text_lower, ['time', 'weather', 'news']):
                intent['category'] = 'information'
                intent['confidence'] = 0.8
                intent['action'] = 'get'
                
                if self._matches_patterns(text_lower, ['time']):
                    intent['target'] = 'time'
                elif self._matches_patterns(text_lower, ['weather']):
                    intent['target'] = 'weather'
                elif self._matches_patterns(text_lower, ['news']):
                    intent['target'] = 'news'
                    
            # Check for search patterns
            elif self._matches_patterns(text_lower, ['search', 'find', 'look']):
                intent['category'] = 'web_search'
                intent['action'] = 'search'
                intent['confidence'] = 0.8
                
            return intent
            
        except Exception as e:
            self.logger.error(f"Error analyzing intent: {e}")
            return {
                'action': 'unknown',
                'target': 'unknown',
                'category': 'general',
                'confidence': 0.1,
                'language': 'unknown',
                'original_text': text,
                'timestamp': datetime.now().isoformat(),
                'entities': {},
                'error': str(e)
            }
            
    def _matches_patterns(self, text: str, keywords: List[str]) -> bool:
        """Check if text matches any of the keywords."""
        for keyword in keywords:
            if keyword in text:
                return True
        return False
        
    def _extract_target(self, text: str, action: str) -> str:
        """Extract target from command text."""
        words = text.lower().split()
        
        # Find action word index
        action_index = -1
        for i, word in enumerate(words):
            if action in word:
                action_index = i
                break
                
        # Get words after action
        if action_index >= 0 and action_index < len(words) - 1:
            target_words = words[action_index + 1:]
            return ' '.join(target_words[:2])  # Take next 2 words
            
        return 'unknown'
        
    def _detect_language(self, text: str) -> str:
        """Simple language detection."""
        hindi_chars = re.findall(r'[\u0900-\u097F]', text)
        english_words = re.findall(r'[a-zA-Z]+', text)
        
        if hindi_chars and english_words:
            return 'mixed'
        elif hindi_chars:
            return 'hindi'
        elif english_words:
            return 'english'
        else:
            return 'unknown'
