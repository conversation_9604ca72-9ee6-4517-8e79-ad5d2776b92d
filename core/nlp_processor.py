"""
NLP Processor - Natural Language Processing for JARVIS
Handles Hindi and English mixed language understanding
"""

import re
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import openai
from transformers import pipeline


class NLPProcessor:
    """Natural Language Processing engine for JARVIS."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Language patterns
        self.hindi_patterns = {
            'open': ['खोलो', 'चालू करो', 'शुरू करो', 'open'],
            'close': ['बंद करो', 'close', 'band'],
            'search': ['खोजो', 'search', 'dhundo'],
            'play': ['चलाओ', 'play', 'chalao'],
            'stop': ['रोको', 'stop', 'roko'],
            'volume': ['आवाज़', 'volume', 'awaz'],
            'weather': ['मौसम', 'weather', 'mausam'],
            'time': ['समय', 'time', 'samay'],
            'email': ['ईमेल', 'email', 'mail'],
            'message': ['संदेश', 'message', 'sandesh'],
        }
        
        # Intent categories
        self.intent_categories = {
            'system_control': ['open', 'close', 'volume', 'brightness'],
            'web_search': ['search', 'find', 'lookup'],
            'media_control': ['play', 'stop', 'pause', 'next', 'previous'],
            'information': ['weather', 'time', 'news', 'calendar'],
            'communication': ['email', 'message', 'call'],
            'file_operations': ['create', 'delete', 'move', 'copy'],
        }
        
    async def initialize(self):
        """Initialize NLP components."""
        self.logger.info("Initializing NLP Processor...")
        
        try:
            # Initialize OpenAI
            openai.api_key = self.config.get('openai_api_key')
            
            # Initialize sentiment analysis
            self.sentiment_analyzer = pipeline(
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest"
            )
            
            self.logger.info("✅ NLP Processor initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize NLP Processor: {e}")
            raise
            
    async def analyze_intent(self, text: str, context: List[Dict] = None) -> Dict[str, Any]:
        """Analyze user intent from text."""
        text = text.lower().strip()
        
        # Extract entities and intent
        entities = self._extract_entities(text)
        action = self._extract_action(text)
        target = self._extract_target(text)
        
        # Determine intent category
        category = self._categorize_intent(action)
        
        # Get sentiment
        sentiment = self._analyze_sentiment(text)
        
        # Build context from conversation history
        conversation_context = self._build_context(context) if context else {}
        
        intent = {
            'original_text': text,
            'action': action,
            'target': target,
            'entities': entities,
            'category': category,
            'sentiment': sentiment,
            'context': conversation_context,
            'confidence': self._calculate_confidence(text, action, target),
            'language': self._detect_language(text),
            'timestamp': datetime.now().isoformat()
        }
        
        self.logger.debug(f"Intent analysis: {intent}")
        return intent
        
    def _extract_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract named entities from text."""
        entities = {
            'applications': [],
            'files': [],
            'websites': [],
            'contacts': [],
            'locations': [],
            'numbers': [],
            'times': []
        }
        
        # Application names
        app_patterns = [
            r'\b(safari|chrome|firefox|mail|messages|spotify|itunes|finder|terminal|vscode|xcode)\b',
            r'\b(whatsapp|telegram|slack|zoom|teams|skype)\b',
            r'\b(photoshop|illustrator|premiere|final cut|logic)\b'
        ]
        
        for pattern in app_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities['applications'].extend(matches)
            
        # File extensions
        file_pattern = r'\b\w+\.(txt|pdf|doc|docx|jpg|png|mp3|mp4|zip)\b'
        entities['files'] = re.findall(file_pattern, text, re.IGNORECASE)
        
        # Websites
        website_pattern = r'\b(?:www\.)?[\w-]+\.(?:com|org|net|edu|gov|in)\b'
        entities['websites'] = re.findall(website_pattern, text, re.IGNORECASE)
        
        # Numbers
        number_pattern = r'\b\d+(?:\.\d+)?\b'
        entities['numbers'] = re.findall(number_pattern, text)
        
        # Time expressions
        time_patterns = [
            r'\b(?:at\s+)?(\d{1,2}:\d{2}(?:\s*[ap]m)?)\b',
            r'\b(morning|afternoon|evening|night|today|tomorrow|yesterday)\b'
        ]
        
        for pattern in time_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            entities['times'].extend(matches)
            
        return entities
        
    def _extract_action(self, text: str) -> str:
        """Extract the main action from text."""
        # Check Hindi patterns first
        for action, patterns in self.hindi_patterns.items():
            for pattern in patterns:
                if pattern in text:
                    return action
                    
        # English action words
        action_patterns = {
            'open': r'\b(open|launch|start|run|execute)\b',
            'close': r'\b(close|quit|exit|terminate|kill)\b',
            'search': r'\b(search|find|look|google|browse)\b',
            'play': r'\b(play|start|begin|resume)\b',
            'stop': r'\b(stop|pause|halt|end)\b',
            'create': r'\b(create|make|new|generate)\b',
            'delete': r'\b(delete|remove|trash|destroy)\b',
            'send': r'\b(send|email|message|text)\b',
            'call': r'\b(call|phone|dial)\b',
            'set': r'\b(set|adjust|change|modify)\b',
            'get': r'\b(get|fetch|retrieve|show|tell)\b',
        }
        
        for action, pattern in action_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                return action
                
        return 'unknown'
        
    def _extract_target(self, text: str) -> str:
        """Extract the target object/application from text."""
        # Remove action words to focus on target
        cleaned_text = text
        for patterns in self.hindi_patterns.values():
            for pattern in patterns:
                cleaned_text = cleaned_text.replace(pattern, '')
                
        # Common targets
        targets = {
            'safari': ['safari', 'browser'],
            'mail': ['mail', 'email', 'ईमेल'],
            'music': ['music', 'spotify', 'itunes', 'संगीत'],
            'messages': ['messages', 'whatsapp', 'telegram'],
            'finder': ['finder', 'files', 'फाइल'],
            'terminal': ['terminal', 'command'],
            'system': ['system', 'computer', 'mac'],
            'volume': ['volume', 'sound', 'आवाज़'],
            'brightness': ['brightness', 'screen'],
            'weather': ['weather', 'मौसम'],
            'time': ['time', 'clock', 'समय'],
        }
        
        for target, keywords in targets.items():
            for keyword in keywords:
                if keyword in cleaned_text.lower():
                    return target
                    
        # Extract remaining words as potential target
        words = cleaned_text.strip().split()
        if words:
            return ' '.join(words[:2])  # Take first two words
            
        return 'unknown'
        
    def _categorize_intent(self, action: str) -> str:
        """Categorize the intent based on action."""
        for category, actions in self.intent_categories.items():
            if action in actions:
                return category
        return 'general'
        
    def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of the text."""
        try:
            result = self.sentiment_analyzer(text)[0]
            return {
                'label': result['label'],
                'score': result['score']
            }
        except Exception as e:
            self.logger.warning(f"Sentiment analysis failed: {e}")
            return {'label': 'NEUTRAL', 'score': 0.5}
            
    def _detect_language(self, text: str) -> str:
        """Detect if text contains Hindi, English, or mixed."""
        hindi_chars = len(re.findall(r'[\u0900-\u097F]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        if hindi_chars > 0 and english_chars > 0:
            return 'mixed'
        elif hindi_chars > 0:
            return 'hindi'
        elif english_chars > 0:
            return 'english'
        else:
            return 'unknown'
            
    def _build_context(self, conversation_history: List[Dict]) -> Dict[str, Any]:
        """Build context from conversation history."""
        if not conversation_history:
            return {}
            
        recent_commands = [item['command'] for item in conversation_history[-3:]]
        recent_actions = [self._extract_action(cmd) for cmd in recent_commands]
        
        return {
            'recent_commands': recent_commands,
            'recent_actions': recent_actions,
            'conversation_length': len(conversation_history)
        }
        
    def _calculate_confidence(self, text: str, action: str, target: str) -> float:
        """Calculate confidence score for intent analysis."""
        confidence = 0.5  # Base confidence
        
        # Boost confidence for known actions
        if action != 'unknown':
            confidence += 0.2
            
        # Boost confidence for known targets
        if target != 'unknown':
            confidence += 0.2
            
        # Boost confidence for clear command structure
        if any(word in text for word in ['please', 'कृपया', 'jarvis']):
            confidence += 0.1
            
        return min(confidence, 1.0)
