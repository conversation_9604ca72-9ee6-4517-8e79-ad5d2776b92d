#!/usr/bin/env python3
"""
JARVIS - AI Assistant
Main entry point for the JARVIS AI assistant system.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.jarvis_core import <PERSON><PERSON><PERSON>
from gui.main_interface import Jarvis<PERSON><PERSON>
from utils.config_manager import ConfigManager
from utils.logger import setup_logging


class JarvisApplication:
    """Main JARVIS application class."""
    
    def __init__(self):
        self.config = ConfigManager()
        self.logger = setup_logging(self.config)
        self.jarvis_core = None
        self.gui = None
        
    async def initialize(self):
        """Initialize all JARVIS components."""
        try:
            self.logger.info("🚀 Initializing JARVIS...")
            
            # Initialize core AI system
            self.jarvis_core = JarvisCore(self.config)
            await self.jarvis_core.initialize()
            
            # Initialize GUI
            self.gui = JarvisGUI(self.config, self.jarvis_core)
            
            self.logger.info("✅ JARVIS initialization complete!")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize JARVIS: {e}")
            raise
            
    async def run(self):
        """Run the JARVIS application."""
        try:
            await self.initialize()
            
            # Start the GUI in the main thread
            self.logger.info("🎯 Starting JARVIS interface...")
            await self.gui.run()
            
        except KeyboardInterrupt:
            self.logger.info("👋 JARVIS shutting down...")
        except Exception as e:
            self.logger.error(f"💥 JARVIS encountered an error: {e}")
            raise
        finally:
            await self.cleanup()
            
    async def cleanup(self):
        """Clean up resources."""
        if self.jarvis_core:
            await self.jarvis_core.shutdown()
        if self.gui:
            await self.gui.cleanup()


def main():
    """Main entry point."""
    print("🤖 JARVIS - AI Assistant")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        sys.exit(1)
    
    # Create and run application
    app = JarvisApplication()
    
    try:
        # Run the async application
        asyncio.run(app.run())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
